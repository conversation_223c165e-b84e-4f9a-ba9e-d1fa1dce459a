"""
岗位管理序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from apps.users.serializers import UserSerializer
from .models import PositionCategory, Position, PositionApplication, PositionFavorite, PositionView


class PositionCategorySerializer(serializers.ModelSerializer):
    """岗位分类序列化器"""
    
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = PositionCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent', 'sort_order',
            'is_active', 'children', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_children(self, obj):
        """获取子分类"""
        if hasattr(obj, 'children'):
            children = obj.children.filter(is_active=True).order_by('sort_order')
            return PositionCategorySerializer(children, many=True).data
        return []


class PositionSerializer(serializers.ModelSerializer):
    """岗位基础序列化器"""
    
    category = PositionCategorySerializer(read_only=True)
    created_by = UserSerializer(read_only=True)
    salary_range = serializers.ReadOnlyField()
    is_active = serializers.ReadOnlyField()
    is_deadline_passed = serializers.ReadOnlyField()
    
    class Meta:
        model = Position
        fields = [
            'id', 'title', 'code', 'category', 'description', 'responsibilities',
            'requirements', 'department', 'organization', 'province', 'city',
            'district', 'address', 'location', 'salary_min', 'salary_max',
            'salary_description', 'benefits', 'education_required', 'major_required',
            'experience_required', 'age_min', 'age_max', 'work_type',
            'recruitment_count', 'application_deadline', 'view_count',
            'application_count', 'favorite_count', 'status', 'is_featured',
            'is_urgent', 'published_at', 'created_by', 'salary_range',
            'is_active', 'is_deadline_passed', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'code', 'view_count', 'application_count', 'favorite_count',
            'published_at', 'created_by', 'created_at', 'updated_at'
        ]


class PositionCreateSerializer(serializers.ModelSerializer):
    """岗位创建序列化器"""
    
    category_id = serializers.IntegerField(write_only=True, required=False)
    
    class Meta:
        model = Position
        fields = [
            'title', 'category_id', 'description', 'responsibilities', 'requirements',
            'department', 'organization', 'province', 'city', 'district', 'address',
            'location', 'salary_min', 'salary_max', 'salary_description', 'benefits',
            'education_required', 'major_required', 'experience_required', 'age_min',
            'age_max', 'work_type', 'recruitment_count', 'application_deadline',
            'status', 'is_featured', 'is_urgent'
        ]
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                category = PositionCategory.objects.get(id=value, is_active=True)
                return category
            except PositionCategory.DoesNotExist:
                raise serializers.ValidationError("岗位分类不存在或已禁用")
        return None
    
    def validate(self, attrs):
        """验证岗位数据"""
        # 验证薪资范围
        salary_min = attrs.get('salary_min')
        salary_max = attrs.get('salary_max')
        
        if salary_min and salary_max and salary_min > salary_max:
            raise serializers.ValidationError("最低薪资不能大于最高薪资")
        
        # 验证年龄范围
        age_min = attrs.get('age_min')
        age_max = attrs.get('age_max')
        
        if age_min and age_max and age_min > age_max:
            raise serializers.ValidationError("最小年龄不能大于最大年龄")
        
        # 验证申请截止时间
        application_deadline = attrs.get('application_deadline')
        if application_deadline and application_deadline <= timezone.now():
            raise serializers.ValidationError("申请截止时间必须是未来时间")
        
        return attrs
    
    def create(self, validated_data):
        """创建岗位"""
        category = validated_data.pop('category_id', None)
        user = self.context['request'].user
        
        # 生成岗位编号
        import uuid
        code = f"POS{timezone.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:6].upper()}"
        
        position = Position.objects.create(
            code=code,
            category=category,
            created_by=user,
            **validated_data
        )
        
        return position


class PositionListSerializer(serializers.ModelSerializer):
    """岗位列表序列化器"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    salary_range = serializers.ReadOnlyField()
    is_active = serializers.ReadOnlyField()
    
    class Meta:
        model = Position
        fields = [
            'id', 'title', 'department', 'location', 'category_name',
            'salary_range', 'education_required', 'recruitment_count',
            'application_count', 'status', 'is_featured', 'is_urgent',
            'is_active', 'published_at', 'created_at'
        ]


class PositionUpdateSerializer(serializers.ModelSerializer):
    """岗位更新序列化器"""
    
    category_id = serializers.IntegerField(write_only=True, required=False)
    
    class Meta:
        model = Position
        fields = [
            'title', 'category_id', 'description', 'responsibilities', 'requirements',
            'department', 'organization', 'province', 'city', 'district', 'address',
            'location', 'salary_min', 'salary_max', 'salary_description', 'benefits',
            'education_required', 'major_required', 'experience_required', 'age_min',
            'age_max', 'work_type', 'recruitment_count', 'application_deadline',
            'status', 'is_featured', 'is_urgent'
        ]
    
    def validate_category_id(self, value):
        """验证分类ID"""
        if value:
            try:
                category = PositionCategory.objects.get(id=value, is_active=True)
                return category
            except PositionCategory.DoesNotExist:
                raise serializers.ValidationError("岗位分类不存在或已禁用")
        return None
    
    def validate(self, attrs):
        """验证岗位数据"""
        # 复用创建时的验证逻辑
        return PositionCreateSerializer().validate(attrs)
    
    def update(self, instance, validated_data):
        """更新岗位"""
        category = validated_data.pop('category_id', None)
        if category is not None:
            instance.category = category
        
        return super().update(instance, validated_data)


class PositionApplicationSerializer(serializers.ModelSerializer):
    """岗位申请序列化器"""
    
    position = PositionListSerializer(read_only=True)
    candidate = UserSerializer(read_only=True)
    reviewed_by = UserSerializer(read_only=True)
    
    class Meta:
        model = PositionApplication
        fields = [
            'id', 'position', 'candidate', 'cover_letter', 'resume', 'status',
            'reviewed_by', 'reviewed_at', 'review_notes', 'interview_time',
            'interview_location', 'interview_notes', 'applied_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'reviewed_by', 'reviewed_at', 'applied_at', 'updated_at'
        ]


class PositionApplicationCreateSerializer(serializers.ModelSerializer):
    """岗位申请创建序列化器"""
    
    position_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = PositionApplication
        fields = ['position_id', 'cover_letter', 'resume']
    
    def validate_position_id(self, value):
        """验证岗位ID"""
        try:
            position = Position.objects.get(id=value, status='active')
        except Position.DoesNotExist:
            raise serializers.ValidationError("岗位不存在或未开放申请")
        
        # 检查申请截止时间
        if position.is_deadline_passed:
            raise serializers.ValidationError("申请截止时间已过")
        
        # 检查是否已申请
        user = self.context['request'].user
        if PositionApplication.objects.filter(position=position, candidate=user).exists():
            raise serializers.ValidationError("您已申请过此岗位")
        
        return value
    
    def create(self, validated_data):
        """创建申请记录"""
        position_id = validated_data.pop('position_id')
        position = Position.objects.get(id=position_id)
        user = self.context['request'].user
        
        application = PositionApplication.objects.create(
            position=position,
            candidate=user,
            **validated_data
        )
        
        # 更新岗位申请人数
        position.application_count = position.applications.count()
        position.save(update_fields=['application_count'])
        
        return application


class PositionFavoriteSerializer(serializers.ModelSerializer):
    """岗位收藏序列化器"""
    
    position = PositionListSerializer(read_only=True)
    candidate = UserSerializer(read_only=True)
    
    class Meta:
        model = PositionFavorite
        fields = ['id', 'position', 'candidate', 'created_at']
        read_only_fields = ['id', 'created_at']


class PositionSearchSerializer(serializers.Serializer):
    """岗位搜索序列化器"""
    
    keyword = serializers.CharField(required=False, help_text="关键词")
    category_id = serializers.IntegerField(required=False, help_text="分类ID")
    location = serializers.CharField(required=False, help_text="工作地点")
    education_required = serializers.CharField(required=False, help_text="学历要求")
    salary_min = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, help_text="最低薪资")
    salary_max = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, help_text="最高薪资")
    work_type = serializers.CharField(required=False, help_text="工作类型")
    is_featured = serializers.BooleanField(required=False, help_text="是否推荐")
    is_urgent = serializers.BooleanField(required=False, help_text="是否紧急")
    
    def validate(self, attrs):
        """验证搜索参数"""
        salary_min = attrs.get('salary_min')
        salary_max = attrs.get('salary_max')
        
        if salary_min and salary_max and salary_min > salary_max:
            raise serializers.ValidationError("最低薪资不能大于最高薪资")
        
        return attrs
