"""
岗位管理应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    PositionCategoryViewSet, PositionViewSet, PositionApplicationViewSet,
    PositionFavoriteViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'categories', PositionCategoryViewSet)
router.register(r'positions', PositionViewSet)
router.register(r'applications', PositionApplicationViewSet)
router.register(r'favorites', PositionFavoriteViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
