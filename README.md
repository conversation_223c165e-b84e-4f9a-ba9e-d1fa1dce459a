# 黔南州政策性考试成绩测试岗位推荐系统

## 项目简介

本系统是一个针对黔南州政策性考试（如公务员考试、事业单位招聘等）的成绩测试与岗位推荐系统。系统整合了考试成绩管理、岗位信息查询、智能推荐算法等功能，为考生提供一站式的考试服务。

## 系统架构

- **后端**: Django + Django REST Framework
- **前端**: Vue.js 用户端 + Vue.js Web管理端
- **数据库**: MySQL + Redis
- **推荐算法**: 协同过滤 + 基于内容的推荐
- **部署**: Docker + Nginx

## 功能特性

### 考生功能
- 用户注册与登录
- 个人信息管理
- 考试成绩查询
- 岗位信息浏览与搜索
- 智能岗位推荐
- 岗位收藏与申请
- 准考证打印
- 成绩统计分析

### 管理员功能
- 用户管理
- 考试信息管理
- 岗位信息管理
- 成绩管理
- 系统配置
- 数据备份与恢复

### 招聘单位功能
- 岗位信息发布与管理
- 考生简历查看
- 申请人员筛选
- 招聘进度管理

## 项目结构

```
qiannan-exam-system/
├── backend/                 # Django后端
│   ├── apps/               # 应用模块
│   │   ├── users/          # 用户管理
│   │   ├── exams/          # 考试管理
│   │   ├── positions/      # 岗位管理
│   │   ├── recommendations/ # 推荐系统
│   │   └── analytics/      # 数据分析
│   ├── config/             # 配置文件
│   ├── utils/              # 工具函数
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端项目
│   ├── web-user/           # 用户端网页
│   └── admin/              # Web管理端
├── docs/                   # 项目文档
├── deploy/                 # 部署配置
└── README.md
```

## 开发环境要求

- Python 3.8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+

## 快速开始

### 后端启动

```bash
cd backend
pip install -r requirements.txt
python manage.py migrate
python manage.py runserver
```

### 前端启动

```bash
# Web管理端
cd frontend/web-admin
npm install
npm run dev

# 微信小程序
使用微信开发者工具打开 frontend/miniprogram 目录
```

## 开发进度

- [x] 项目初始化
- [ ] 数据库设计
- [ ] 用户认证系统
- [ ] 考试管理模块
- [ ] 岗位管理模块
- [ ] 智能推荐算法
- [ ] 数据可视化
- [ ] 前端界面开发
- [ ] 系统集成测试
- [ ] 部署上线

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题，请联系项目维护者。
