"""
微信相关工具函数
"""
import requests
import json
import logging
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class WeChatAPI:
    """微信API工具类"""
    
    def __init__(self):
        self.app_id = settings.WECHAT_MINIPROGRAM.get('APP_ID')
        self.app_secret = settings.WECHAT_MINIPROGRAM.get('APP_SECRET')
        
        if not self.app_id or not self.app_secret:
            raise ValueError("微信小程序配置不完整，请检查APP_ID和APP_SECRET")
    
    def code_to_session(self, code):
        """
        通过code获取session_key和openid
        
        Args:
            code (str): 微信授权码
            
        Returns:
            dict: 包含openid、session_key等信息
        """
        url = "https://api.weixin.qq.com/sns/jscode2session"
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'js_code': code,
            'grant_type': 'authorization_code'
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'errcode' in data:
                logger.error(f"微信API错误: {data}")
                raise WeChatAPIError(data.get('errmsg', '未知错误'), data.get('errcode'))
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"微信API请求失败: {e}")
            raise WeChatAPIError("网络请求失败")
        except json.JSONDecodeError as e:
            logger.error(f"微信API响应解析失败: {e}")
            raise WeChatAPIError("响应数据格式错误")
    
    def get_access_token(self):
        """
        获取access_token
        
        Returns:
            str: access_token
        """
        cache_key = f"wechat_access_token_{self.app_id}"
        access_token = cache.get(cache_key)
        
        if access_token:
            return access_token
        
        url = "https://api.weixin.qq.com/cgi-bin/token"
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'errcode' in data:
                logger.error(f"获取access_token失败: {data}")
                raise WeChatAPIError(data.get('errmsg', '未知错误'), data.get('errcode'))
            
            access_token = data['access_token']
            expires_in = data.get('expires_in', 7200)
            
            # 缓存access_token，提前5分钟过期
            cache.set(cache_key, access_token, expires_in - 300)
            
            return access_token
            
        except requests.RequestException as e:
            logger.error(f"获取access_token请求失败: {e}")
            raise WeChatAPIError("网络请求失败")
        except json.JSONDecodeError as e:
            logger.error(f"access_token响应解析失败: {e}")
            raise WeChatAPIError("响应数据格式错误")
    
    def decrypt_data(self, encrypted_data, iv, session_key):
        """
        解密微信加密数据
        
        Args:
            encrypted_data (str): 加密数据
            iv (str): 初始向量
            session_key (str): 会话密钥
            
        Returns:
            dict: 解密后的数据
        """
        try:
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            import base64
            
            # Base64解码
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            # AES解密
            cipher = Cipher(
                algorithms.AES(session_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # 去除PKCS7填充
            pad = decrypted[-1]
            decrypted = decrypted[:-pad]
            
            # 解析JSON
            result = json.loads(decrypted.decode('utf-8'))
            
            # 验证水印
            watermark = result.get('watermark', {})
            if watermark.get('appid') != self.app_id:
                raise WeChatAPIError("数据水印验证失败")
            
            return result
            
        except Exception as e:
            logger.error(f"微信数据解密失败: {e}")
            raise WeChatAPIError("数据解密失败")
    
    def get_user_phone(self, code):
        """
        获取用户手机号
        
        Args:
            code (str): 手机号授权码
            
        Returns:
            dict: 包含手机号信息
        """
        access_token = self.get_access_token()
        url = f"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}"
        
        data = {
            'code': code
        }
        
        try:
            response = requests.post(url, json=data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('errcode') != 0:
                logger.error(f"获取手机号失败: {result}")
                raise WeChatAPIError(result.get('errmsg', '未知错误'), result.get('errcode'))
            
            return result.get('phone_info', {})
            
        except requests.RequestException as e:
            logger.error(f"获取手机号请求失败: {e}")
            raise WeChatAPIError("网络请求失败")
        except json.JSONDecodeError as e:
            logger.error(f"手机号响应解析失败: {e}")
            raise WeChatAPIError("响应数据格式错误")


class WeChatAPIError(Exception):
    """微信API异常"""
    
    def __init__(self, message, errcode=None):
        self.message = message
        self.errcode = errcode
        super().__init__(self.message)


def validate_wechat_signature(signature, timestamp, nonce, token):
    """
    验证微信签名
    
    Args:
        signature (str): 微信签名
        timestamp (str): 时间戳
        nonce (str): 随机数
        token (str): 令牌
        
    Returns:
        bool: 验证结果
    """
    import hashlib
    
    # 将token、timestamp、nonce三个参数进行字典序排序
    tmp_arr = [token, timestamp, nonce]
    tmp_arr.sort()
    
    # 将三个参数字符串拼接成一个字符串进行sha1加密
    tmp_str = ''.join(tmp_arr)
    tmp_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()
    
    # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    return tmp_str == signature


def generate_wechat_qrcode(scene, page=None, width=430):
    """
    生成微信小程序二维码
    
    Args:
        scene (str): 场景值
        page (str): 页面路径
        width (int): 二维码宽度
        
    Returns:
        bytes: 二维码图片数据
    """
    wechat_api = WeChatAPI()
    access_token = wechat_api.get_access_token()
    
    url = f"https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={access_token}"
    
    data = {
        'scene': scene,
        'width': width
    }
    
    if page:
        data['page'] = page
    
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        
        # 检查是否返回错误信息
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            error_data = response.json()
            logger.error(f"生成二维码失败: {error_data}")
            raise WeChatAPIError(error_data.get('errmsg', '未知错误'), error_data.get('errcode'))
        
        return response.content
        
    except requests.RequestException as e:
        logger.error(f"生成二维码请求失败: {e}")
        raise WeChatAPIError("网络请求失败")


# 创建全局实例
wechat_api = WeChatAPI()
