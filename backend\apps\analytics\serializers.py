"""
数据分析序列化器
"""
from rest_framework import serializers
from .models import (
    SystemStatistics, ExamAnalytics, PositionAnalytics, 
    UserAnalytics, RecommendationAnalytics
)


class SystemStatisticsSerializer(serializers.ModelSerializer):
    """系统统计序列化器"""
    
    class Meta:
        model = SystemStatistics
        fields = [
            'id', 'stat_type', 'stat_date', 'total_users', 'new_users',
            'active_users', 'candidate_users', 'recruiter_users', 'total_exams',
            'new_exams', 'ongoing_exams', 'total_registrations', 'new_registrations',
            'total_positions', 'new_positions', 'active_positions', 'total_applications',
            'new_applications', 'total_recommendations', 'new_recommendations',
            'recommendation_clicks', 'recommendation_applications', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ExamAnalyticsSerializer(serializers.ModelSerializer):
    """考试分析序列化器"""
    
    exam_name = serializers.CharField(source='exam.name', read_only=True)
    
    class Meta:
        model = ExamAnalytics
        fields = [
            'id', 'exam', 'exam_name', 'registration_count', 'approved_registration_count',
            'participant_count', 'attendance_rate', 'score_count', 'qualified_count',
            'qualification_rate', 'avg_initial_score', 'avg_assessment_score',
            'avg_total_score', 'max_total_score', 'min_total_score', 'score_distribution',
            'region_distribution', 'education_distribution', 'age_distribution',
            'calculated_at', 'updated_at'
        ]
        read_only_fields = ['id', 'calculated_at', 'updated_at']


class PositionAnalyticsSerializer(serializers.ModelSerializer):
    """岗位分析序列化器"""
    
    position_title = serializers.CharField(source='position.title', read_only=True)
    
    class Meta:
        model = PositionAnalytics
        fields = [
            'id', 'position', 'position_title', 'total_views', 'unique_views',
            'daily_views', 'total_applications', 'pending_applications',
            'approved_applications', 'rejected_applications', 'view_to_application_rate',
            'application_approval_rate', 'total_favorites', 'competition_ratio',
            'applicant_education_distribution', 'applicant_age_distribution',
            'applicant_region_distribution', 'recommendation_count',
            'recommendation_click_count', 'recommendation_application_count',
            'calculated_at', 'updated_at'
        ]
        read_only_fields = ['id', 'calculated_at', 'updated_at']


class UserAnalyticsSerializer(serializers.ModelSerializer):
    """用户分析序列化器"""
    
    user_name = serializers.CharField(source='user.display_name', read_only=True)
    
    class Meta:
        model = UserAnalytics
        fields = [
            'id', 'user', 'user_name', 'login_count', 'last_login_date',
            'active_days', 'position_view_count', 'exam_view_count',
            'position_application_count', 'exam_registration_count',
            'position_favorite_count', 'recommendation_received_count',
            'recommendation_clicked_count', 'recommendation_applied_count',
            'preferred_locations', 'preferred_categories', 'preferred_salary_range',
            'application_success_rate', 'calculated_at', 'updated_at'
        ]
        read_only_fields = ['id', 'calculated_at', 'updated_at']


class RecommendationAnalyticsSerializer(serializers.ModelSerializer):
    """推荐分析序列化器"""
    
    class Meta:
        model = RecommendationAnalytics
        fields = [
            'id', 'algorithm_type', 'date', 'total_recommendations', 'unique_users',
            'unique_positions', 'click_count', 'application_count', 'favorite_count',
            'click_through_rate', 'application_rate', 'conversion_rate',
            'positive_feedback_count', 'negative_feedback_count', 'avg_rating',
            'user_coverage', 'position_coverage', 'calculated_at'
        ]
        read_only_fields = ['id', 'calculated_at']


class DashboardDataSerializer(serializers.Serializer):
    """仪表板数据序列化器"""
    
    # 概览数据
    overview = serializers.DictField()
    
    # 趋势数据
    user_trend = serializers.ListField()
    exam_trend = serializers.ListField()
    position_trend = serializers.ListField()
    application_trend = serializers.ListField()
    
    # 分布数据
    user_type_distribution = serializers.ListField()
    education_distribution = serializers.ListField()
    location_distribution = serializers.ListField()
    
    # 推荐效果
    recommendation_performance = serializers.ListField()


class ExamReportSerializer(serializers.Serializer):
    """考试报告序列化器"""
    
    exam_id = serializers.IntegerField()
    exam_name = serializers.CharField()
    
    # 基础统计
    basic_stats = serializers.DictField()
    
    # 成绩分析
    score_analysis = serializers.DictField()
    
    # 分布分析
    distributions = serializers.DictField()
    
    # 排名信息
    rankings = serializers.ListField()


class PositionReportSerializer(serializers.Serializer):
    """岗位报告序列化器"""
    
    position_id = serializers.IntegerField()
    position_title = serializers.CharField()
    
    # 基础统计
    basic_stats = serializers.DictField()
    
    # 申请分析
    application_analysis = serializers.DictField()
    
    # 竞争分析
    competition_analysis = serializers.DictField()
    
    # 推荐效果
    recommendation_performance = serializers.DictField()


class UserBehaviorAnalysisSerializer(serializers.Serializer):
    """用户行为分析序列化器"""
    
    user_id = serializers.IntegerField()
    user_name = serializers.CharField()
    
    # 活跃度分析
    activity_analysis = serializers.DictField()
    
    # 偏好分析
    preference_analysis = serializers.DictField()
    
    # 行为模式
    behavior_patterns = serializers.DictField()
    
    # 推荐效果
    recommendation_effectiveness = serializers.DictField()


class TrendAnalysisSerializer(serializers.Serializer):
    """趋势分析序列化器"""
    
    period = serializers.CharField(help_text="时间周期：daily, weekly, monthly")
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    
    # 用户趋势
    user_trends = serializers.ListField()
    
    # 考试趋势
    exam_trends = serializers.ListField()
    
    # 岗位趋势
    position_trends = serializers.ListField()
    
    # 推荐趋势
    recommendation_trends = serializers.ListField()


class ComparisonAnalysisSerializer(serializers.Serializer):
    """对比分析序列化器"""
    
    comparison_type = serializers.CharField(help_text="对比类型：exam, position, algorithm")
    items = serializers.ListField(help_text="对比项目列表")
    
    # 对比结果
    comparison_results = serializers.ListField()
    
    # 差异分析
    difference_analysis = serializers.DictField()
    
    # 建议
    recommendations = serializers.ListField()


class PredictionAnalysisSerializer(serializers.Serializer):
    """预测分析序列化器"""
    
    prediction_type = serializers.CharField(help_text="预测类型：user_growth, application_trend, success_rate")
    time_horizon = serializers.IntegerField(help_text="预测时间范围（天）")
    
    # 历史数据
    historical_data = serializers.ListField()
    
    # 预测结果
    predictions = serializers.ListField()
    
    # 置信区间
    confidence_intervals = serializers.ListField()
    
    # 影响因素
    influencing_factors = serializers.DictField()


class AnalyticsRequestSerializer(serializers.Serializer):
    """分析请求序列化器"""
    
    analysis_type = serializers.ChoiceField(
        choices=[
            ('dashboard', '仪表板'),
            ('exam_report', '考试报告'),
            ('position_report', '岗位报告'),
            ('user_behavior', '用户行为分析'),
            ('trend_analysis', '趋势分析'),
            ('comparison', '对比分析'),
            ('prediction', '预测分析'),
        ],
        help_text="分析类型"
    )
    
    # 时间范围
    start_date = serializers.DateField(required=False, help_text="开始日期")
    end_date = serializers.DateField(required=False, help_text="结束日期")
    
    # 筛选条件
    filters = serializers.DictField(required=False, help_text="筛选条件")
    
    # 分组条件
    group_by = serializers.CharField(required=False, help_text="分组字段")
    
    # 排序条件
    order_by = serializers.CharField(required=False, help_text="排序字段")
    
    # 限制数量
    limit = serializers.IntegerField(required=False, default=100, help_text="结果数量限制")


class ExportRequestSerializer(serializers.Serializer):
    """导出请求序列化器"""
    
    export_type = serializers.ChoiceField(
        choices=[
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('pdf', 'PDF'),
        ],
        default='excel',
        help_text="导出格式"
    )
    
    data_type = serializers.ChoiceField(
        choices=[
            ('system_stats', '系统统计'),
            ('exam_analytics', '考试分析'),
            ('position_analytics', '岗位分析'),
            ('user_analytics', '用户分析'),
            ('recommendation_analytics', '推荐分析'),
        ],
        help_text="数据类型"
    )
    
    # 时间范围
    start_date = serializers.DateField(required=False, help_text="开始日期")
    end_date = serializers.DateField(required=False, help_text="结束日期")
    
    # 筛选条件
    filters = serializers.DictField(required=False, help_text="筛选条件")
    
    # 包含字段
    fields = serializers.ListField(required=False, help_text="包含的字段列表")
    
    def validate(self, attrs):
        """验证导出请求"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("开始日期不能晚于结束日期")
        
        return attrs
