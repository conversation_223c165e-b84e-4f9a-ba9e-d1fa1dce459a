"""
数据分析管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count, Avg
from .models import (
    SystemStatistics, ExamAnalytics, PositionAnalytics, 
    UserAnalytics, RecommendationAnalytics
)


@admin.register(SystemStatistics)
class SystemStatisticsAdmin(admin.ModelAdmin):
    """系统统计管理"""
    
    list_display = [
        'stat_type', 'stat_date', 'total_users', 'new_users', 'active_users',
        'total_exams', 'total_positions', 'total_applications', 'created_at'
    ]
    list_filter = ['stat_type', 'stat_date', 'created_at']
    search_fields = ['stat_type']
    ordering = ['-stat_date']
    date_hierarchy = 'stat_date'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['stat_type', 'stat_date']
        }),
        ('用户统计', {
            'fields': [
                'total_users', 'new_users', 'active_users', 
                'candidate_users', 'recruiter_users'
            ]
        }),
        ('考试统计', {
            'fields': [
                'total_exams', 'new_exams', 'ongoing_exams',
                'total_registrations', 'new_registrations'
            ]
        }),
        ('岗位统计', {
            'fields': [
                'total_positions', 'new_positions', 'active_positions',
                'total_applications', 'new_applications'
            ]
        }),
        ('推荐统计', {
            'fields': [
                'total_recommendations', 'new_recommendations',
                'recommendation_clicks', 'recommendation_applications'
            ]
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        """禁止手动添加统计记录"""
        return False


@admin.register(ExamAnalytics)
class ExamAnalyticsAdmin(admin.ModelAdmin):
    """考试分析管理"""
    
    list_display = [
        'exam_name', 'registration_count', 'participant_count', 'attendance_rate',
        'qualified_count', 'qualification_rate', 'avg_total_score', 'calculated_at'
    ]
    list_filter = ['calculated_at']
    search_fields = ['exam__name']
    ordering = ['-calculated_at']
    
    fieldsets = [
        ('考试信息', {
            'fields': ['exam']
        }),
        ('报名统计', {
            'fields': [
                'registration_count', 'approved_registration_count',
                'participant_count', 'attendance_rate'
            ]
        }),
        ('成绩统计', {
            'fields': [
                'score_count', 'qualified_count', 'qualification_rate',
                'avg_initial_score', 'avg_assessment_score', 'avg_total_score',
                'max_total_score', 'min_total_score'
            ]
        }),
        ('分布分析', {
            'fields': [
                'score_distribution', 'region_distribution',
                'education_distribution', 'age_distribution'
            ],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['calculated_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at', 'updated_at']
    
    def exam_name(self, obj):
        """考试名称"""
        return obj.exam.name
    exam_name.short_description = '考试名称'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('exam')
    
    def has_add_permission(self, request):
        """禁止手动添加分析记录"""
        return False


@admin.register(PositionAnalytics)
class PositionAnalyticsAdmin(admin.ModelAdmin):
    """岗位分析管理"""
    
    list_display = [
        'position_title', 'total_views', 'total_applications', 'total_favorites',
        'competition_ratio', 'view_to_application_rate', 'calculated_at'
    ]
    list_filter = ['calculated_at']
    search_fields = ['position__title', 'position__department']
    ordering = ['-calculated_at']
    
    fieldsets = [
        ('岗位信息', {
            'fields': ['position']
        }),
        ('浏览统计', {
            'fields': ['total_views', 'unique_views', 'daily_views']
        }),
        ('申请统计', {
            'fields': [
                'total_applications', 'pending_applications',
                'approved_applications', 'rejected_applications'
            ]
        }),
        ('转化率分析', {
            'fields': [
                'view_to_application_rate', 'application_approval_rate',
                'total_favorites', 'competition_ratio'
            ]
        }),
        ('申请人分析', {
            'fields': [
                'applicant_education_distribution', 'applicant_age_distribution',
                'applicant_region_distribution'
            ],
            'classes': ['collapse']
        }),
        ('推荐统计', {
            'fields': [
                'recommendation_count', 'recommendation_click_count',
                'recommendation_application_count'
            ]
        }),
        ('时间信息', {
            'fields': ['calculated_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at', 'updated_at']
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位标题'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('position')
    
    def has_add_permission(self, request):
        """禁止手动添加分析记录"""
        return False


@admin.register(UserAnalytics)
class UserAnalyticsAdmin(admin.ModelAdmin):
    """用户分析管理"""
    
    list_display = [
        'user_name', 'login_count', 'active_days', 'position_view_count',
        'position_application_count', 'application_success_rate', 'calculated_at'
    ]
    list_filter = ['calculated_at', 'last_login_date']
    search_fields = ['user__username', 'user__real_name']
    ordering = ['-calculated_at']
    
    fieldsets = [
        ('用户信息', {
            'fields': ['user']
        }),
        ('活跃度统计', {
            'fields': ['login_count', 'last_login_date', 'active_days']
        }),
        ('浏览行为', {
            'fields': ['position_view_count', 'exam_view_count']
        }),
        ('申请行为', {
            'fields': [
                'position_application_count', 'exam_registration_count',
                'position_favorite_count'
            ]
        }),
        ('推荐互动', {
            'fields': [
                'recommendation_received_count', 'recommendation_clicked_count',
                'recommendation_applied_count'
            ]
        }),
        ('偏好分析', {
            'fields': [
                'preferred_locations', 'preferred_categories',
                'preferred_salary_range'
            ],
            'classes': ['collapse']
        }),
        ('成功率', {
            'fields': ['application_success_rate']
        }),
        ('时间信息', {
            'fields': ['calculated_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at', 'updated_at']
    
    def user_name(self, obj):
        """用户姓名"""
        return obj.user.display_name
    user_name.short_description = '用户姓名'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
    
    def has_add_permission(self, request):
        """禁止手动添加分析记录"""
        return False


@admin.register(RecommendationAnalytics)
class RecommendationAnalyticsAdmin(admin.ModelAdmin):
    """推荐分析管理"""
    
    list_display = [
        'algorithm_type', 'date', 'total_recommendations', 'click_through_rate',
        'application_rate', 'conversion_rate', 'avg_rating', 'calculated_at'
    ]
    list_filter = ['algorithm_type', 'date', 'calculated_at']
    search_fields = ['algorithm_type']
    ordering = ['-date']
    date_hierarchy = 'date'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['algorithm_type', 'date']
        }),
        ('推荐统计', {
            'fields': [
                'total_recommendations', 'unique_users', 'unique_positions'
            ]
        }),
        ('效果统计', {
            'fields': [
                'click_count', 'application_count', 'favorite_count'
            ]
        }),
        ('转化率', {
            'fields': [
                'click_through_rate', 'application_rate', 'conversion_rate'
            ]
        }),
        ('用户反馈', {
            'fields': [
                'positive_feedback_count', 'negative_feedback_count', 'avg_rating'
            ]
        }),
        ('覆盖率', {
            'fields': ['user_coverage', 'position_coverage']
        }),
        ('时间信息', {
            'fields': ['calculated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at']
    
    def has_add_permission(self, request):
        """禁止手动添加分析记录"""
        return False
    
    actions = ['recalculate_analytics']
    
    def recalculate_analytics(self, request, queryset):
        """重新计算分析数据"""
        # 这里可以触发重新计算的任务
        from .tasks import update_recommendation_analytics
        update_recommendation_analytics.delay()
        
        self.message_user(request, "已触发推荐分析数据重新计算")
    recalculate_analytics.short_description = "重新计算分析数据"
