<template>
  <div class="home">
    <!-- 轮播图 -->
    <section class="hero-section">
      <el-carousel height="400px" indicator-position="outside">
        <el-carousel-item v-for="item in banners" :key="item.id">
          <div class="carousel-item" :style="{ backgroundImage: `url(${item.image})` }">
            <div class="carousel-content">
              <h2>{{ item.title }}</h2>
              <p>{{ item.description }}</p>
              <el-button type="primary" size="large" @click="handleBannerClick(item)">
                {{ item.buttonText }}
              </el-button>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </section>

    <!-- 快捷入口 -->
    <section class="quick-access">
      <div class="container">
        <div class="quick-grid">
          <div class="quick-item" @click="$router.push('/exams')">
            <el-icon size="40"><Document /></el-icon>
            <h3>考试报名</h3>
            <p>查看最新考试信息并在线报名</p>
          </div>
          <div class="quick-item" @click="$router.push('/positions')">
            <el-icon size="40"><Suitcase /></el-icon>
            <h3>岗位查看</h3>
            <p>浏览各类招聘岗位信息</p>
          </div>
          <div class="quick-item" @click="handleRecommendations">
            <el-icon size="40"><Star /></el-icon>
            <h3>智能推荐</h3>
            <p>基于AI算法的个性化推荐</p>
          </div>
          <div class="quick-item" @click="handleScores">
            <el-icon size="40"><TrendCharts /></el-icon>
            <h3>成绩查询</h3>
            <p>查询考试成绩和排名信息</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 最新考试 -->
    <section class="latest-exams">
      <div class="container">
        <div class="section-header">
          <h2>最新考试</h2>
          <el-button type="primary" link @click="$router.push('/exams')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="exam-grid">
          <div v-for="exam in latestExams" :key="exam.id" class="exam-card" @click="$router.push(`/exams/${exam.id}`)">
            <div class="exam-status">
              <el-tag v-if="exam.is_registration_open" type="success">报名中</el-tag>
              <el-tag v-else-if="exam.is_ongoing" type="warning">进行中</el-tag>
              <el-tag v-else type="info">已结束</el-tag>
            </div>
            <h3>{{ exam.name }}</h3>
            <div class="exam-info">
              <p><el-icon><Calendar /></el-icon> 考试时间：{{ exam.exam_date }}</p>
              <p><el-icon><Location /></el-icon> 考试地点：{{ exam.exam_location }}</p>
              <p><el-icon><User /></el-icon> 报名人数：{{ exam.registration_count }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门岗位 -->
    <section class="hot-positions">
      <div class="container">
        <div class="section-header">
          <h2>热门岗位</h2>
          <el-button type="primary" link @click="$router.push('/positions')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="position-grid">
          <div v-for="position in hotPositions" :key="position.id" class="position-card" @click="$router.push(`/positions/${position.id}`)">
            <div class="position-header">
              <h3>{{ position.title }}</h3>
              <div class="position-tags">
                <el-tag v-if="position.is_featured" type="danger" size="small">推荐</el-tag>
                <el-tag v-if="position.is_urgent" type="warning" size="small">紧急</el-tag>
              </div>
            </div>
            <div class="position-info">
              <p><el-icon><OfficeBuilding /></el-icon> {{ position.department }}</p>
              <p><el-icon><Location /></el-icon> {{ position.location }}</p>
              <p><el-icon><Money /></el-icon> {{ position.salary_range }}</p>
            </div>
            <div class="position-meta">
              <span>招聘{{ position.recruitment_count }}人</span>
              <span>{{ position.application_count }}人申请</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 个性化推荐 -->
    <section v-if="isLoggedIn && recommendations.length > 0" class="recommendations">
      <div class="container">
        <div class="section-header">
          <h2>为您推荐</h2>
          <el-button type="primary" link @click="$router.push('/recommendations')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="recommendation-grid">
          <div v-for="item in recommendations" :key="item.id" class="recommendation-card" @click="handleRecommendationClick(item)">
            <div class="recommendation-score">
              <el-progress type="circle" :percentage="item.score" :width="60" />
              <span>匹配度</span>
            </div>
            <div class="recommendation-content">
              <h3>{{ item.position.title }}</h3>
              <p>{{ item.reason }}</p>
              <div class="recommendation-info">
                <span>{{ item.position.department }}</span>
                <span>{{ item.position.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 系统公告 -->
    <section v-if="notices.length > 0" class="notices">
      <div class="container">
        <div class="section-header">
          <h2>系统公告</h2>
        </div>
        <div class="notice-list">
          <div v-for="notice in notices" :key="notice.id" class="notice-item" @click="handleNoticeClick(notice)">
            <el-icon color="#ff6b35"><Bell /></el-icon>
            <span class="notice-title">{{ notice.title }}</span>
            <span class="notice-time">{{ notice.created_at }}</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import api from '@/api'

export default {
  name: 'Home',
  
  data() {
    return {
      banners: [
        {
          id: 1,
          title: '2024年黔南州公务员考试',
          description: '面向全州招录优秀人才，报名即将开始',
          buttonText: '立即报名',
          image: '/images/banner1.jpg',
          link: '/exams'
        },
        {
          id: 2,
          title: '事业单位招聘',
          description: '多个岗位等你来，福利待遇优厚',
          buttonText: '查看岗位',
          image: '/images/banner2.jpg',
          link: '/positions'
        },
        {
          id: 3,
          title: '智能岗位推荐',
          description: 'AI算法为您精准匹配合适岗位',
          buttonText: '体验推荐',
          image: '/images/banner3.jpg',
          link: '/recommendations'
        }
      ],
      latestExams: [],
      hotPositions: [],
      recommendations: [],
      notices: [],
      loading: false
    }
  },
  
  computed: {
    ...mapGetters(['isLoggedIn'])
  },
  
  async mounted() {
    await this.loadPageData()
  },
  
  methods: {
    async loadPageData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadLatestExams(),
          this.loadHotPositions(),
          this.loadNotices()
        ])
        
        if (this.isLoggedIn) {
          await this.loadRecommendations()
        }
      } catch (error) {
        console.error('加载页面数据失败:', error)
        this.$message.error('加载数据失败，请刷新页面重试')
      } finally {
        this.loading = false
      }
    },
    
    async loadLatestExams() {
      const response = await api.exam.getExamList({
        page: 1,
        page_size: 3,
        ordering: '-created_at'
      })
      this.latestExams = response.results
    },
    
    async loadHotPositions() {
      const response = await api.position.getPositionList({
        page: 1,
        page_size: 6,
        ordering: '-application_count,-view_count'
      })
      this.hotPositions = response.results
    },
    
    async loadRecommendations() {
      try {
        const response = await api.recommendation.generateRecommendations({
          algorithm_type: 'hybrid',
          count: 3
        })
        this.recommendations = response.map(item => ({
          ...item,
          score: Math.round(item.score * 100)
        }))
      } catch (error) {
        console.error('加载推荐数据失败:', error)
      }
    },
    
    async loadNotices() {
      try {
        const response = await api.common.getNotices({
          page: 1,
          page_size: 5
        })
        this.notices = response.results
      } catch (error) {
        console.error('加载公告失败:', error)
      }
    },
    
    handleBannerClick(banner) {
      if (banner.link) {
        this.$router.push(banner.link)
      }
    },
    
    handleRecommendations() {
      if (!this.isLoggedIn) {
        this.$router.push('/login')
        return
      }
      this.$router.push('/recommendations')
    },
    
    handleScores() {
      if (!this.isLoggedIn) {
        this.$router.push('/login')
        return
      }
      this.$router.push('/profile/scores')
    },
    
    async handleRecommendationClick(item) {
      try {
        // 记录点击行为
        await api.recommendation.clickRecommendation(item.record_id)
      } catch (error) {
        console.error('记录推荐点击失败:', error)
      }
      
      // 跳转到岗位详情
      this.$router.push(`/positions/${item.position.id}`)
    },
    
    handleNoticeClick(notice) {
      // 这里可以打开公告详情弹窗或跳转到详情页
      this.$message.info('公告详情功能待开发')
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

// 轮播图样式
.hero-section {
  .carousel-item {
    height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
    }
  }
  
  .carousel-content {
    text-align: center;
    color: white;
    z-index: 1;
    
    h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      font-weight: bold;
    }
    
    p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
  }
}

// 快捷入口样式
.quick-access {
  padding: 60px 0;
  background: white;
  
  .quick-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
  }
  
  .quick-item {
    text-align: center;
    padding: 30px 20px;
    border-radius: 12px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      background: white;
    }
    
    .el-icon {
      color: #409eff;
      margin-bottom: 15px;
    }
    
    h3 {
      margin: 15px 0 10px;
      color: #333;
      font-size: 1.2rem;
    }
    
    p {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }
}

// 通用section样式
section {
  padding: 60px 0;
  
  &:nth-child(even) {
    background: #f8f9fa;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  
  h2 {
    font-size: 2rem;
    color: #333;
    margin: 0;
  }
}

// 考试卡片样式
.exam-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.exam-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .exam-status {
    margin-bottom: 15px;
  }
  
  h3 {
    margin: 0 0 15px;
    color: #333;
    font-size: 1.3rem;
  }
  
  .exam-info {
    p {
      margin: 8px 0;
      color: #666;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }
}

// 岗位卡片样式
.position-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.position-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .position-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
      color: #333;
      font-size: 1.2rem;
      flex: 1;
    }
    
    .position-tags {
      display: flex;
      gap: 5px;
    }
  }
  
  .position-info {
    margin-bottom: 15px;
    
    p {
      margin: 8px 0;
      color: #666;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }
  
  .position-meta {
    display: flex;
    justify-content: space-between;
    color: #999;
    font-size: 0.9rem;
  }
}

// 推荐卡片样式
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.recommendation-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 20px;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .recommendation-score {
    text-align: center;
    
    span {
      display: block;
      margin-top: 10px;
      color: #666;
      font-size: 0.9rem;
    }
  }
  
  .recommendation-content {
    flex: 1;
    
    h3 {
      margin: 0 0 10px;
      color: #333;
      font-size: 1.2rem;
    }
    
    p {
      margin: 0 0 10px;
      color: #666;
      line-height: 1.5;
    }
    
    .recommendation-info {
      display: flex;
      gap: 15px;
      color: #999;
      font-size: 0.9rem;
    }
  }
}

// 公告样式
.notice-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.notice-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #f8f9fa;
  }
  
  .el-icon {
    margin-right: 15px;
  }
  
  .notice-title {
    flex: 1;
    color: #333;
  }
  
  .notice-time {
    color: #999;
    font-size: 0.9rem;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section .carousel-content {
    h2 {
      font-size: 1.8rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .quick-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .exam-grid,
  .position-grid {
    grid-template-columns: 1fr;
  }
  
  .recommendation-card {
    flex-direction: column;
    text-align: center;
  }
  
  section {
    padding: 40px 0;
  }
}
</style>
