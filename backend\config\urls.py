"""
黔南州政策性考试成绩测试岗位推荐系统 URL配置
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# API文档配置
schema_view = get_schema_view(
    openapi.Info(
        title="黔南州考试系统 API",
        default_version='v1',
        description="黔南州政策性考试成绩测试岗位推荐系统API文档",
        terms_of_service="https://www.example.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

urlpatterns = [
    # 管理后台
    path('admin/', admin.site.urls),
    
    # API文档
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('swagger.json', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    
    # API路由
    path('api/v1/users/', include('apps.users.urls')),
    path('api/v1/exams/', include('apps.exams.urls')),
    path('api/v1/positions/', include('apps.positions.urls')),
    path('api/v1/recommendations/', include('apps.recommendations.urls')),
    path('api/v1/analytics/', include('apps.analytics.urls')),
]

# 开发环境下的静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # 开发工具
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns

# 自定义管理后台标题
admin.site.site_header = '黔南州考试系统管理后台'
admin.site.site_title = '黔南州考试系统'
admin.site.index_title = '欢迎使用黔南州考试系统管理后台'
