"""
岗位管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import PositionCategory, Position, PositionApplication, PositionFavorite, PositionView


@admin.register(PositionCategory)
class PositionCategoryAdmin(admin.ModelAdmin):
    """岗位分类管理"""
    
    list_display = ['name', 'code', 'parent', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['sort_order', 'name']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['name', 'code', 'description', 'parent']
        }),
        ('设置', {
            'fields': ['sort_order', 'is_active']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    """岗位管理"""
    
    list_display = [
        'title', 'department', 'location', 'category', 'salary_range_display',
        'recruitment_count', 'application_count', 'status', 'is_featured',
        'created_at'
    ]
    list_filter = [
        'status', 'category', 'education_required', 'work_type',
        'is_featured', 'is_urgent', 'created_at'
    ]
    search_fields = ['title', 'department', 'organization', 'location']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['title', 'code', 'category', 'department', 'organization']
        }),
        ('岗位描述', {
            'fields': ['description', 'responsibilities', 'requirements']
        }),
        ('工作地点', {
            'fields': ['province', 'city', 'district', 'address', 'location']
        }),
        ('薪资待遇', {
            'fields': ['salary_min', 'salary_max', 'salary_description', 'benefits']
        }),
        ('招聘要求', {
            'fields': [
                'education_required', 'major_required', 'experience_required',
                'age_min', 'age_max', 'work_type'
            ]
        }),
        ('招聘设置', {
            'fields': ['recruitment_count', 'application_deadline']
        }),
        ('状态设置', {
            'fields': ['status', 'is_featured', 'is_urgent']
        }),
        ('统计信息', {
            'fields': [
                'view_count', 'application_count', 'favorite_count',
                'published_at'
            ],
            'classes': ['collapse']
        }),
        ('创建信息', {
            'fields': ['created_by', 'created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = [
        'code', 'view_count', 'application_count', 'favorite_count',
        'published_at', 'created_by', 'created_at', 'updated_at'
    ]
    
    def salary_range_display(self, obj):
        """薪资范围显示"""
        return obj.salary_range
    salary_range_display.short_description = '薪资范围'
    
    def save_model(self, request, obj, form, change):
        """保存时设置创建者"""
        if not change:  # 新建时
            obj.created_by = request.user
        
        # 如果状态改为active且未设置发布时间，则设置发布时间
        if obj.status == 'active' and not obj.published_at:
            obj.published_at = timezone.now()
        
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category', 'created_by')
    
    actions = ['make_featured', 'remove_featured', 'make_active', 'make_paused']
    
    def make_featured(self, request, queryset):
        """设为推荐"""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"已将 {updated} 个岗位设为推荐")
    make_featured.short_description = "设为推荐岗位"
    
    def remove_featured(self, request, queryset):
        """取消推荐"""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f"已将 {updated} 个岗位取消推荐")
    remove_featured.short_description = "取消推荐岗位"
    
    def make_active(self, request, queryset):
        """设为招聘中"""
        updated = queryset.update(status='active', published_at=timezone.now())
        self.message_user(request, f"已将 {updated} 个岗位设为招聘中")
    make_active.short_description = "设为招聘中"
    
    def make_paused(self, request, queryset):
        """暂停招聘"""
        updated = queryset.update(status='paused')
        self.message_user(request, f"已将 {updated} 个岗位暂停招聘")
    make_paused.short_description = "暂停招聘"


@admin.register(PositionApplication)
class PositionApplicationAdmin(admin.ModelAdmin):
    """岗位申请管理"""
    
    list_display = [
        'position_title', 'candidate_name', 'status', 'applied_at',
        'reviewed_at', 'reviewed_by'
    ]
    list_filter = ['status', 'applied_at', 'reviewed_at']
    search_fields = [
        'position__title', 'candidate__username', 'candidate__real_name'
    ]
    ordering = ['-applied_at']
    date_hierarchy = 'applied_at'
    
    fieldsets = [
        ('申请信息', {
            'fields': ['position', 'candidate', 'cover_letter', 'resume']
        }),
        ('审核信息', {
            'fields': ['status', 'reviewed_by', 'reviewed_at', 'review_notes']
        }),
        ('面试信息', {
            'fields': ['interview_time', 'interview_location', 'interview_notes']
        }),
        ('时间信息', {
            'fields': ['applied_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['applied_at', 'updated_at', 'reviewed_at']
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位'
    
    def candidate_name(self, obj):
        """申请人姓名"""
        return obj.candidate.real_name or obj.candidate.username
    candidate_name.short_description = '申请人'
    
    def save_model(self, request, obj, form, change):
        """保存时设置审核信息"""
        if 'status' in form.changed_data and obj.status in ['approved', 'rejected']:
            obj.reviewed_by = request.user
            obj.reviewed_at = timezone.now()
        
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'position', 'candidate', 'reviewed_by'
        )
    
    actions = ['approve_applications', 'reject_applications']
    
    def approve_applications(self, request, queryset):
        """批量审核通过"""
        updated = queryset.filter(status='pending').update(
            status='approved',
            reviewed_by=request.user,
            reviewed_at=timezone.now()
        )
        self.message_user(request, f"已审核通过 {updated} 个申请")
    approve_applications.short_description = "批量审核通过"
    
    def reject_applications(self, request, queryset):
        """批量审核拒绝"""
        updated = queryset.filter(status='pending').update(
            status='rejected',
            reviewed_by=request.user,
            reviewed_at=timezone.now()
        )
        self.message_user(request, f"已审核拒绝 {updated} 个申请")
    reject_applications.short_description = "批量审核拒绝"


@admin.register(PositionFavorite)
class PositionFavoriteAdmin(admin.ModelAdmin):
    """岗位收藏管理"""
    
    list_display = ['position_title', 'candidate_name', 'created_at']
    list_filter = ['created_at']
    search_fields = [
        'position__title', 'candidate__username', 'candidate__real_name'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位'
    
    def candidate_name(self, obj):
        """收藏人姓名"""
        return obj.candidate.real_name or obj.candidate.username
    candidate_name.short_description = '收藏人'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('position', 'candidate')


@admin.register(PositionView)
class PositionViewAdmin(admin.ModelAdmin):
    """岗位浏览记录管理"""
    
    list_display = ['position_title', 'user_name', 'ip_address', 'viewed_at']
    list_filter = ['viewed_at']
    search_fields = [
        'position__title', 'user__username', 'user__real_name', 'ip_address'
    ]
    ordering = ['-viewed_at']
    date_hierarchy = 'viewed_at'
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位'
    
    def user_name(self, obj):
        """浏览者姓名"""
        return obj.user.real_name or obj.user.username
    user_name.short_description = '浏览者'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('position', 'user')
    
    def has_add_permission(self, request):
        """禁止手动添加浏览记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改浏览记录"""
        return False
