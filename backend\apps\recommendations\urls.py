"""
推荐系统应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    UserBehaviorViewSet, UserPreferenceViewSet, RecommendationViewSet,
    RecommendationFeedbackViewSet, RecommendationAnalyticsView
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'behaviors', UserBehaviorViewSet)
router.register(r'preferences', UserPreferenceViewSet)
router.register(r'recommendations', RecommendationViewSet)
router.register(r'feedbacks', RecommendationFeedbackViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('analytics/', RecommendationAnalyticsView.as_view(), name='recommendation-analytics'),
]
