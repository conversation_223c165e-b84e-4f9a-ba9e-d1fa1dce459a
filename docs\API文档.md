# 黔南州考试系统 API 文档

## 概述

本文档描述了黔南州政策性考试成绩测试岗位推荐系统的API接口规范。

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1/`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "success": true,
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "success": false,
  "data": null
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "获取成功",
  "success": true,
  "data": {
    "results": [],
    "pagination": {
      "count": 100,
      "page": 1,
      "page_size": 20,
      "total_pages": 5,
      "has_next": true,
      "has_previous": false,
      "next_page": 2,
      "previous_page": null
    }
  }
}
```

## 认证接口

### 微信登录
- **URL**: `/users/wechat-login/`
- **方法**: POST
- **描述**: 微信小程序授权登录

**请求参数**:
```json
{
  "code": "微信授权码",
  "user_info": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应数据**:
```json
{
  "access_token": "JWT访问令牌",
  "refresh_token": "刷新令牌",
  "user": {
    "id": 1,
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "头像URL",
    "user_type": "candidate"
  }
}
```

## 用户管理接口

### 获取用户信息
- **URL**: `/users/profile/`
- **方法**: GET
- **认证**: 需要

### 更新用户信息
- **URL**: `/users/profile/`
- **方法**: PUT
- **认证**: 需要

## 考试管理接口

### 获取考试列表
- **URL**: `/exams/`
- **方法**: GET
- **认证**: 需要

### 获取考试详情
- **URL**: `/exams/{id}/`
- **方法**: GET
- **认证**: 需要

### 获取成绩信息
- **URL**: `/exams/{id}/scores/`
- **方法**: GET
- **认证**: 需要

## 岗位管理接口

### 获取岗位列表
- **URL**: `/positions/`
- **方法**: GET
- **认证**: 可选

### 搜索岗位
- **URL**: `/positions/search/`
- **方法**: GET
- **认证**: 可选

### 获取岗位详情
- **URL**: `/positions/{id}/`
- **方法**: GET
- **认证**: 可选

### 申请岗位
- **URL**: `/positions/{id}/apply/`
- **方法**: POST
- **认证**: 需要

## 推荐系统接口

### 获取推荐岗位
- **URL**: `/recommendations/positions/`
- **方法**: GET
- **认证**: 需要

### 获取推荐理由
- **URL**: `/recommendations/positions/{id}/reason/`
- **方法**: GET
- **认证**: 需要

## 数据分析接口

### 获取成绩统计
- **URL**: `/analytics/scores/`
- **方法**: GET
- **认证**: 需要

### 获取岗位统计
- **URL**: `/analytics/positions/`
- **方法**: GET
- **认证**: 需要

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有需要认证的接口都需要在请求头中携带JWT Token
2. Token格式：`Authorization: Bearer <token>`
3. 分页参数：`page`（页码）、`page_size`（每页数量）
4. 搜索参数：`search`（关键词）、`ordering`（排序）
5. 时间格式：ISO 8601 格式（如：2023-12-01T10:00:00Z）
