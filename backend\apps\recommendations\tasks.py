"""
推荐系统定时任务
"""
import logging
from datetime import timedelta
from django.utils import timezone
from django.db.models import Count, Q
from celery import shared_task

from apps.users.models import User
from apps.positions.models import Position
from .models import (
    UserBehavior, RecommendationRecord, UserSimilarity, 
    PositionSimilarity, UserPreference
)
from .algorithms import recommendation_service

logger = logging.getLogger(__name__)


@shared_task
def generate_daily_recommendations():
    """生成每日推荐"""
    logger.info("开始生成每日推荐")
    
    # 获取启用推荐且频率为每日的用户
    users = User.objects.filter(
        user_type='candidate',
        is_active=True,
        preference__enable_recommendations=True,
        preference__recommendation_frequency='daily'
    )
    
    success_count = 0
    error_count = 0
    
    for user in users:
        try:
            # 检查今天是否已经生成过推荐
            today = timezone.now().date()
            existing_recommendations = RecommendationRecord.objects.filter(
                user=user,
                recommended_at__date=today
            ).exists()
            
            if existing_recommendations:
                continue
            
            # 生成推荐
            recommendations = recommendation_service.get_recommendations(
                user=user,
                algorithm_type='hybrid',
                count=10
            )
            
            if recommendations:
                # 保存推荐记录
                recommendation_service.save_recommendations(
                    user=user,
                    recommendations=recommendations
                )
                success_count += 1
            
        except Exception as e:
            logger.error(f"为用户 {user.id} 生成推荐失败: {e}")
            error_count += 1
    
    logger.info(f"每日推荐生成完成，成功: {success_count}，失败: {error_count}")
    return {'success': success_count, 'error': error_count}


@shared_task
def generate_weekly_recommendations():
    """生成每周推荐"""
    logger.info("开始生成每周推荐")
    
    # 获取启用推荐且频率为每周的用户
    users = User.objects.filter(
        user_type='candidate',
        is_active=True,
        preference__enable_recommendations=True,
        preference__recommendation_frequency='weekly'
    )
    
    success_count = 0
    error_count = 0
    
    for user in users:
        try:
            # 检查本周是否已经生成过推荐
            week_start = timezone.now().date() - timedelta(days=timezone.now().weekday())
            existing_recommendations = RecommendationRecord.objects.filter(
                user=user,
                recommended_at__date__gte=week_start
            ).exists()
            
            if existing_recommendations:
                continue
            
            # 生成推荐
            recommendations = recommendation_service.get_recommendations(
                user=user,
                algorithm_type='hybrid',
                count=15
            )
            
            if recommendations:
                # 保存推荐记录
                recommendation_service.save_recommendations(
                    user=user,
                    recommendations=recommendations
                )
                success_count += 1
            
        except Exception as e:
            logger.error(f"为用户 {user.id} 生成推荐失败: {e}")
            error_count += 1
    
    logger.info(f"每周推荐生成完成，成功: {success_count}，失败: {error_count}")
    return {'success': success_count, 'error': error_count}


@shared_task
def calculate_user_similarities():
    """计算用户相似度"""
    logger.info("开始计算用户相似度")
    
    # 获取有足够交互数据的用户
    min_interactions = 5
    active_users = User.objects.filter(
        user_type='candidate',
        is_active=True
    ).annotate(
        interaction_count=Count('behaviors')
    ).filter(interaction_count__gte=min_interactions)
    
    if active_users.count() < 2:
        logger.info("活跃用户数量不足，跳过相似度计算")
        return {'message': '活跃用户数量不足'}
    
    # 清理旧的相似度记录（超过7天）
    old_similarities = UserSimilarity.objects.filter(
        calculated_at__lt=timezone.now() - timedelta(days=7)
    )
    deleted_count = old_similarities.count()
    old_similarities.delete()
    
    logger.info(f"清理了 {deleted_count} 条旧的用户相似度记录")
    
    # 计算新的相似度
    from .algorithms import CollaborativeFilteringAlgorithm
    
    calculated_count = 0
    
    for i, user1 in enumerate(active_users):
        for user2 in active_users[i+1:]:
            try:
                # 检查是否已存在相似度记录
                if UserSimilarity.objects.filter(
                    Q(user1=user1, user2=user2) | Q(user1=user2, user2=user1)
                ).exists():
                    continue
                
                # 计算相似度（这里简化实现）
                algorithm = CollaborativeFilteringAlgorithm(user1)
                interaction_matrix = algorithm._build_interaction_matrix()
                
                if not interaction_matrix.empty and user1.id in interaction_matrix.index and user2.id in interaction_matrix.index:
                    from sklearn.metrics.pairwise import cosine_similarity
                    import numpy as np
                    
                    user1_vector = interaction_matrix.loc[user1.id].values.reshape(1, -1)
                    user2_vector = interaction_matrix.loc[user2.id].values.reshape(1, -1)
                    
                    similarity = cosine_similarity(user1_vector, user2_vector)[0][0]
                    
                    if similarity > 0.1:  # 只保存有意义的相似度
                        UserSimilarity.objects.create(
                            user1=user1,
                            user2=user2,
                            similarity_score=similarity,
                            algorithm_type='cosine'
                        )
                        calculated_count += 1
                
            except Exception as e:
                logger.error(f"计算用户 {user1.id} 和 {user2.id} 相似度失败: {e}")
    
    logger.info(f"用户相似度计算完成，新增 {calculated_count} 条记录")
    return {'calculated': calculated_count, 'deleted': deleted_count}


@shared_task
def calculate_position_similarities():
    """计算岗位相似度"""
    logger.info("开始计算岗位相似度")
    
    # 获取活跃岗位
    active_positions = Position.objects.filter(status='active')
    
    if active_positions.count() < 2:
        logger.info("活跃岗位数量不足，跳过相似度计算")
        return {'message': '活跃岗位数量不足'}
    
    # 清理旧的相似度记录
    old_similarities = PositionSimilarity.objects.filter(
        calculated_at__lt=timezone.now() - timedelta(days=7)
    )
    deleted_count = old_similarities.count()
    old_similarities.delete()
    
    logger.info(f"清理了 {deleted_count} 条旧的岗位相似度记录")
    
    # 计算新的相似度
    from .algorithms import ContentBasedAlgorithm
    
    calculated_count = 0
    
    for i, position1 in enumerate(active_positions):
        for position2 in active_positions[i+1:]:
            try:
                # 检查是否已存在相似度记录
                if PositionSimilarity.objects.filter(
                    Q(position1=position1, position2=position2) | 
                    Q(position1=position2, position2=position1)
                ).exists():
                    continue
                
                # 计算基于内容的相似度
                algorithm = ContentBasedAlgorithm(None)
                features1 = algorithm.get_position_features(position1)
                features2 = algorithm.get_position_features(position2)
                
                # 计算特征相似度
                similarity_score = 0
                feature_similarities = {}
                
                for feature in features1:
                    if feature in features2:
                        if isinstance(features1[feature], (int, float)) and isinstance(features2[feature], (int, float)):
                            # 数值特征使用归一化差值
                            max_val = max(features1[feature], features2[feature])
                            if max_val > 0:
                                feature_sim = 1 - abs(features1[feature] - features2[feature]) / max_val
                            else:
                                feature_sim = 1.0
                        else:
                            # 分类特征使用完全匹配
                            feature_sim = 1.0 if features1[feature] == features2[feature] else 0.0
                        
                        feature_similarities[feature] = feature_sim
                        similarity_score += feature_sim
                
                # 归一化相似度分数
                if len(features1) > 0:
                    similarity_score /= len(features1)
                
                if similarity_score > 0.3:  # 只保存有意义的相似度
                    PositionSimilarity.objects.create(
                        position1=position1,
                        position2=position2,
                        similarity_score=similarity_score,
                        algorithm_type='content_based',
                        feature_similarities=feature_similarities
                    )
                    calculated_count += 1
                
            except Exception as e:
                logger.error(f"计算岗位 {position1.id} 和 {position2.id} 相似度失败: {e}")
    
    logger.info(f"岗位相似度计算完成，新增 {calculated_count} 条记录")
    return {'calculated': calculated_count, 'deleted': deleted_count}


@shared_task
def cleanup_expired_recommendations():
    """清理过期推荐"""
    logger.info("开始清理过期推荐")
    
    # 清理超过30天的推荐记录
    expired_date = timezone.now() - timedelta(days=30)
    expired_recommendations = RecommendationRecord.objects.filter(
        recommended_at__lt=expired_date
    )
    
    deleted_count = expired_recommendations.count()
    expired_recommendations.delete()
    
    # 标记过期但未删除的推荐为已过期状态
    RecommendationRecord.objects.filter(
        expires_at__lt=timezone.now(),
        status__in=['active', 'clicked']
    ).update(status='expired')
    
    logger.info(f"清理过期推荐完成，删除 {deleted_count} 条记录")
    return {'deleted': deleted_count}


@shared_task
def update_recommendation_analytics():
    """更新推荐分析数据"""
    logger.info("开始更新推荐分析数据")
    
    from apps.analytics.models import RecommendationAnalytics
    from django.db.models import Count, Avg
    
    # 获取今天的日期
    today = timezone.now().date()
    
    # 统计各算法的推荐效果
    algorithms = ['collaborative_filtering', 'content_based', 'hybrid', 'popularity']
    
    for algorithm in algorithms:
        try:
            # 获取今天的推荐数据
            today_recommendations = RecommendationRecord.objects.filter(
                algorithm_type=algorithm,
                recommended_at__date=today
            )
            
            if not today_recommendations.exists():
                continue
            
            # 计算统计数据
            stats = today_recommendations.aggregate(
                total_recommendations=Count('id'),
                unique_users=Count('user', distinct=True),
                unique_positions=Count('position', distinct=True),
                click_count=Count('id', filter=Q(status='clicked')),
                application_count=Count('id', filter=Q(status='applied')),
                favorite_count=Count('id', filter=Q(status='favorited')),
                avg_rating=Avg('user_rating')
            )
            
            # 计算转化率
            total = stats['total_recommendations']
            click_rate = (stats['click_count'] / total * 100) if total > 0 else 0
            application_rate = (stats['application_count'] / total * 100) if total > 0 else 0
            conversion_rate = application_rate  # 申请率作为转化率
            
            # 更新或创建分析记录
            analytics, created = RecommendationAnalytics.objects.update_or_create(
                algorithm_type=algorithm,
                date=today,
                defaults={
                    'total_recommendations': stats['total_recommendations'],
                    'unique_users': stats['unique_users'],
                    'unique_positions': stats['unique_positions'],
                    'click_count': stats['click_count'],
                    'application_count': stats['application_count'],
                    'favorite_count': stats['favorite_count'],
                    'click_through_rate': round(click_rate, 2),
                    'application_rate': round(application_rate, 2),
                    'conversion_rate': round(conversion_rate, 2),
                    'avg_rating': stats['avg_rating'] or 0,
                }
            )
            
            logger.info(f"更新了算法 {algorithm} 的分析数据")
            
        except Exception as e:
            logger.error(f"更新算法 {algorithm} 分析数据失败: {e}")
    
    logger.info("推荐分析数据更新完成")
    return {'message': '推荐分析数据更新完成'}
