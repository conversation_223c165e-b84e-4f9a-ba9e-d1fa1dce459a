"""
岗位管理模型
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator
from apps.users.models import User


class PositionCategory(models.Model):
    """岗位分类"""
    
    name = models.CharField('分类名称', max_length=100)
    code = models.CharField('分类代码', max_length=20, unique=True)
    description = models.TextField('分类描述', blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True,
                              related_name='children', verbose_name='父分类')
    
    # 排序
    sort_order = models.IntegerField('排序', default=0)
    
    # 状态
    is_active = models.BooleanField('是否启用', default=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'positions_category'
        verbose_name = '岗位分类'
        verbose_name_plural = '岗位分类'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name


class Position(models.Model):
    """岗位信息"""
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('active', '招聘中'),
        ('paused', '暂停招聘'),
        ('closed', '已关闭'),
        ('filled', '已招满'),
    ]
    
    EDUCATION_CHOICES = [
        ('high_school', '高中及以上'),
        ('junior_college', '大专及以上'),
        ('bachelor', '本科及以上'),
        ('master', '硕士及以上'),
        ('doctor', '博士及以上'),
    ]
    
    WORK_TYPE_CHOICES = [
        ('full_time', '全职'),
        ('part_time', '兼职'),
        ('contract', '合同工'),
        ('intern', '实习'),
    ]
    
    # 基本信息
    title = models.CharField('岗位名称', max_length=200)
    code = models.CharField('岗位编号', max_length=50, unique=True, blank=True, null=True)
    category = models.ForeignKey(PositionCategory, on_delete=models.SET_NULL, blank=True, null=True,
                                verbose_name='岗位分类', related_name='positions')
    
    # 岗位描述
    description = models.TextField('岗位描述', blank=True, null=True)
    responsibilities = models.TextField('工作职责', blank=True, null=True)
    requirements = models.TextField('任职要求', blank=True, null=True)
    
    # 单位信息
    department = models.CharField('部门', max_length=100)
    organization = models.CharField('单位名称', max_length=200, blank=True, null=True)
    
    # 工作地点
    province = models.CharField('省份', max_length=50, blank=True, null=True)
    city = models.CharField('城市', max_length=50, blank=True, null=True)
    district = models.CharField('区县', max_length=50, blank=True, null=True)
    address = models.CharField('详细地址', max_length=200, blank=True, null=True)
    location = models.CharField('工作地点', max_length=100)  # 用于搜索和显示
    
    # 薪资待遇
    salary_min = models.DecimalField('最低薪资', max_digits=10, decimal_places=2, blank=True, null=True,
                                    validators=[MinValueValidator(0)])
    salary_max = models.DecimalField('最高薪资', max_digits=10, decimal_places=2, blank=True, null=True,
                                    validators=[MinValueValidator(0)])
    salary_description = models.CharField('薪资描述', max_length=200, blank=True, null=True)
    benefits = models.TextField('福利待遇', blank=True, null=True)
    
    # 招聘要求
    education_required = models.CharField('学历要求', max_length=20, choices=EDUCATION_CHOICES, blank=True, null=True)
    major_required = models.TextField('专业要求', blank=True, null=True)
    experience_required = models.CharField('经验要求', max_length=100, blank=True, null=True)
    age_min = models.IntegerField('最小年龄', blank=True, null=True, validators=[MinValueValidator(16)])
    age_max = models.IntegerField('最大年龄', blank=True, null=True, validators=[MinValueValidator(16)])
    
    # 工作类型
    work_type = models.CharField('工作类型', max_length=20, choices=WORK_TYPE_CHOICES, default='full_time')
    
    # 招聘信息
    recruitment_count = models.IntegerField('招聘人数', default=1, validators=[MinValueValidator(1)])
    application_deadline = models.DateTimeField('申请截止时间', blank=True, null=True)
    
    # 统计信息
    view_count = models.IntegerField('浏览次数', default=0)
    application_count = models.IntegerField('申请人数', default=0)
    favorite_count = models.IntegerField('收藏次数', default=0)
    
    # 状态
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField('是否推荐', default=False)
    is_urgent = models.BooleanField('是否紧急', default=False)
    
    # 发布信息
    published_at = models.DateTimeField('发布时间', blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                  related_name='created_positions', verbose_name='创建人')
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'positions_position'
        verbose_name = '岗位'
        verbose_name_plural = '岗位'
        ordering = ['-is_featured', '-is_urgent', '-published_at']
        indexes = [
            models.Index(fields=['status', 'published_at']),
            models.Index(fields=['location', 'status']),
            models.Index(fields=['education_required', 'status']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.department}"
    
    @property
    def is_active(self):
        """是否处于招聘状态"""
        return self.status == 'active'
    
    @property
    def is_deadline_passed(self):
        """是否已过申请截止时间"""
        if self.application_deadline:
            return timezone.now() > self.application_deadline
        return False
    
    @property
    def salary_range(self):
        """薪资范围显示"""
        if self.salary_min and self.salary_max:
            return f"{self.salary_min}-{self.salary_max}元"
        elif self.salary_min:
            return f"{self.salary_min}元起"
        elif self.salary_description:
            return self.salary_description
        return "面议"


class PositionApplication(models.Model):
    """岗位申请"""
    
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('reviewing', '审核中'),
        ('interview', '面试中'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
        ('withdrawn', '已撤回'),
    ]
    
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位', related_name='applications')
    candidate = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='申请人', related_name='position_applications')
    
    # 申请信息
    cover_letter = models.TextField('求职信', blank=True, null=True)
    resume = models.FileField('简历文件', upload_to='resumes/', blank=True, null=True)
    
    # 状态
    status = models.CharField('申请状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 审核信息
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                   related_name='reviewed_applications', verbose_name='审核人')
    reviewed_at = models.DateTimeField('审核时间', blank=True, null=True)
    review_notes = models.TextField('审核备注', blank=True, null=True)
    
    # 面试信息
    interview_time = models.DateTimeField('面试时间', blank=True, null=True)
    interview_location = models.CharField('面试地点', max_length=200, blank=True, null=True)
    interview_notes = models.TextField('面试备注', blank=True, null=True)
    
    # 时间戳
    applied_at = models.DateTimeField('申请时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'positions_application'
        verbose_name = '岗位申请'
        verbose_name_plural = '岗位申请'
        unique_together = ['position', 'candidate']
        ordering = ['-applied_at']
        indexes = [
            models.Index(fields=['candidate', 'status']),
            models.Index(fields=['position', 'status']),
        ]
    
    def __str__(self):
        return f"{self.candidate.display_name} 申请 {self.position.title}"


class PositionFavorite(models.Model):
    """岗位收藏"""
    
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位', related_name='favorites')
    candidate = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='收藏人', related_name='favorite_positions')
    
    # 时间戳
    created_at = models.DateTimeField('收藏时间', default=timezone.now)
    
    class Meta:
        db_table = 'positions_favorite'
        verbose_name = '岗位收藏'
        verbose_name_plural = '岗位收藏'
        unique_together = ['position', 'candidate']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.candidate.display_name} 收藏 {self.position.title}"


class PositionView(models.Model):
    """岗位浏览记录"""
    
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位', related_name='views')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='浏览者', related_name='position_views')
    
    # 浏览信息
    ip_address = models.GenericIPAddressField('IP地址', blank=True, null=True)
    user_agent = models.TextField('用户代理', blank=True, null=True)
    
    # 时间戳
    viewed_at = models.DateTimeField('浏览时间', default=timezone.now)
    
    class Meta:
        db_table = 'positions_view'
        verbose_name = '岗位浏览记录'
        verbose_name_plural = '岗位浏览记录'
        ordering = ['-viewed_at']
        indexes = [
            models.Index(fields=['position', 'viewed_at']),
            models.Index(fields=['user', 'viewed_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} 浏览 {self.position.title}"
