import { createStore } from 'vuex'
import getters from './getters'

// 自动导入modules
const modulesFiles = require.context('./modules', true, /\.js$/)
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = createStore({
  modules,
  getters,
  
  // 严格模式，在非生产环境下开启
  strict: process.env.NODE_ENV !== 'production'
})

export default store
