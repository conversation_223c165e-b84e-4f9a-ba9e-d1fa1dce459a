"""
数据分析应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    SystemStatisticsViewSet, ExamAnalyticsViewSet, PositionAnalyticsViewSet,
    UserAnalyticsViewSet, RecommendationAnalyticsViewSet, DashboardView,
    AnalyticsView, ExportView
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'system-stats', SystemStatisticsViewSet)
router.register(r'exam-analytics', ExamAnalyticsViewSet)
router.register(r'position-analytics', PositionAnalyticsViewSet)
router.register(r'user-analytics', UserAnalyticsViewSet)
router.register(r'recommendation-analytics', RecommendationAnalyticsViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('dashboard/', DashboardView.as_view(), name='dashboard'),
    path('analyze/', AnalyticsView.as_view(), name='analytics'),
    path('export/', ExportView.as_view(), name='export'),
]
