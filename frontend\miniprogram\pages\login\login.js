// pages/login/login.js
import Toast from '@vant/weapp/toast/toast'
import Dialog from '@vant/weapp/dialog/dialog'
import api from '../../api/index'

const app = getApp()

Page({
  data: {
    // 登录状态
    wechatLoading: false,
    accountLoading: false,
    showAccountLogin: false,
    
    // 表单数据
    username: '',
    password: '',
    
    // 协议相关
    agreedToTerms: false,
    showAgreementPopup: false,
    agreementTitle: '',
    agreementContent: '',
    
    // 来源页面
    redirectUrl: ''
  },

  onLoad(options) {
    console.log('登录页面加载')
    
    // 获取来源页面
    if (options.redirect) {
      this.setData({
        redirectUrl: decodeURIComponent(options.redirect)
      })
    }
    
    // 检查是否已登录
    if (app.globalData.isLogin) {
      this.redirectAfterLogin()
    }
  },

  onShow() {
    console.log('登录页面显示')
  },

  // 微信登录
  async onWechatLogin() {
    if (!this.data.agreedToTerms) {
      Toast.fail('请先同意用户协议和隐私政策')
      return
    }

    this.setData({ wechatLoading: true })

    try {
      // 获取微信登录code
      const loginRes = await this.getWechatLoginCode()
      
      // 获取用户信息
      const userInfo = await this.getWechatUserInfo()
      
      // 调用后端登录接口
      const response = await api.user.wechatLogin({
        code: loginRes.code,
        userInfo: userInfo
      })
      
      // 保存登录信息
      this.saveLoginInfo(response)
      
      Toast.success('登录成功')
      
      // 延迟跳转
      setTimeout(() => {
        this.redirectAfterLogin()
      }, 1500)
      
    } catch (error) {
      console.error('微信登录失败:', error)
      Toast.fail(error.message || '登录失败，请重试')
    } finally {
      this.setData({ wechatLoading: false })
    }
  },

  // 获取微信登录code
  getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 获取微信用户信息
  getWechatUserInfo() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  },

  // 账号密码登录
  async onAccountLogin() {
    if (!this.validateAccountForm()) {
      return
    }

    if (!this.data.agreedToTerms) {
      Toast.fail('请先同意用户协议和隐私政策')
      return
    }

    this.setData({ accountLoading: true })

    try {
      const response = await api.user.login({
        username: this.data.username,
        password: this.data.password
      })
      
      // 保存登录信息
      this.saveLoginInfo(response)
      
      Toast.success('登录成功')
      
      // 延迟跳转
      setTimeout(() => {
        this.redirectAfterLogin()
      }, 1500)
      
    } catch (error) {
      console.error('账号登录失败:', error)
      Toast.fail(error.message || '登录失败，请检查用户名和密码')
    } finally {
      this.setData({ accountLoading: false })
    }
  },

  // 验证账号表单
  validateAccountForm() {
    if (!this.data.username.trim()) {
      Toast.fail('请输入用户名')
      return false
    }
    
    if (!this.data.password.trim()) {
      Toast.fail('请输入密码')
      return false
    }
    
    if (this.data.password.length < 6) {
      Toast.fail('密码长度不能少于6位')
      return false
    }
    
    return true
  },

  // 保存登录信息
  saveLoginInfo(response) {
    const { token, user } = response
    
    // 保存到本地存储
    wx.setStorageSync('token', token)
    wx.setStorageSync('userInfo', user)
    
    // 更新全局状态
    app.globalData.token = token
    app.globalData.userInfo = user
    app.globalData.isLogin = true
  },

  // 登录后跳转
  redirectAfterLogin() {
    if (this.data.redirectUrl) {
      wx.redirectTo({
        url: this.data.redirectUrl
      })
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 表单输入事件
  onUsernameChange(e) {
    this.setData({
      username: e.detail
    })
  },

  onPasswordChange(e) {
    this.setData({
      password: e.detail
    })
  },

  // 切换登录方式
  toggleLoginType() {
    this.setData({
      showAccountLogin: !this.data.showAccountLogin
    })
  },

  // 协议相关事件
  onAgreementChange(e) {
    this.setData({
      agreedToTerms: e.detail
    })
  },

  showUserAgreement() {
    this.setData({
      showAgreementPopup: true,
      agreementTitle: '用户协议',
      agreementContent: this.getUserAgreementContent()
    })
  },

  showPrivacyPolicy() {
    this.setData({
      showAgreementPopup: true,
      agreementTitle: '隐私政策',
      agreementContent: this.getPrivacyPolicyContent()
    })
  },

  closeAgreementPopup() {
    this.setData({
      showAgreementPopup: false
    })
  },

  // 帮助相关事件
  showRegisterHelp() {
    Dialog.alert({
      title: '如何注册账号？',
      message: '1. 使用微信快速登录，系统会自动创建账号\n2. 联系管理员获取账号密码\n3. 通过官方网站注册'
    })
  },

  showLoginHelp() {
    Dialog.alert({
      title: '登录遇到问题？',
      message: '1. 检查网络连接是否正常\n2. 确认用户名和密码是否正确\n3. 尝试使用微信登录\n4. 联系客服获取帮助'
    })
  },

  contactSupport() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 获取用户协议内容
  getUserAgreementContent() {
    return `
欢迎使用黔南州考试岗位推荐系统！

1. 服务说明
本系统为黔南州政策性考试成绩测试岗位推荐平台，为用户提供考试报名、成绩查询、岗位推荐等服务。

2. 用户权利和义务
- 用户有权使用本系统提供的各项服务
- 用户应提供真实、准确的个人信息
- 用户应遵守相关法律法规

3. 隐私保护
我们承诺保护用户隐私，不会泄露用户个人信息。

4. 免责声明
本系统提供的信息仅供参考，最终以官方发布为准。

5. 协议修改
我们保留修改本协议的权利，修改后的协议将在系统内公布。

如有疑问，请联系客服：************
    `
  },

  // 获取隐私政策内容
  getPrivacyPolicyContent() {
    return `
黔南州考试岗位推荐系统隐私政策

1. 信息收集
我们会收集以下信息：
- 基本身份信息（姓名、身份证号等）
- 联系方式（手机号、邮箱等）
- 教育背景和工作经历
- 使用行为数据

2. 信息使用
收集的信息用于：
- 提供考试报名和岗位推荐服务
- 改善用户体验
- 数据分析和统计

3. 信息保护
我们采取以下措施保护用户信息：
- 数据加密传输和存储
- 严格的访问权限控制
- 定期安全审计

4. 信息共享
除法律要求外，我们不会向第三方分享用户个人信息。

5. 用户权利
用户有权：
- 查看和修改个人信息
- 删除个人账户
- 拒绝特定信息处理

联系我们：<EMAIL>
    `
  }
})
