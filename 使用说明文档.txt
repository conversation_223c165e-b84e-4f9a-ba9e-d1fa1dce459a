================================================================================
                    黔南州政策性考试成绩测试岗位推荐系统
                              使用说明文档
================================================================================

一、系统概述
================================================================================

本系统是一个针对黔南州政策性考试（如公务员考试、事业单位招聘等）的成绩测试与
岗位推荐系统。系统整合了考试成绩管理、岗位信息查询、智能推荐算法等功能，为考
生提供一站式的考试服务。

系统特点：
- 支持微信小程序和Web管理端双端访问
- 智能岗位推荐算法，提供个性化推荐
- 完整的考试流程管理，从报名到成绩查询
- 数据可视化分析，直观展示统计信息
- 响应式设计，支持多设备访问

二、环境要求
================================================================================

开发环境：
- Python 3.8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+

生产环境：
- Docker 20.10+
- Docker Compose 1.29+
- 云服务器（推荐2核4G内存以上）

三、快速开始
================================================================================

3.1 开发环境搭建
--------------------------------------------------------------------------------

1. 克隆项目代码
   git clone <项目地址>
   cd qiannan-exam-system

2. 后端环境搭建
   cd backend
   
   # 创建虚拟环境
   python -m venv venv
   
   # 激活虚拟环境
   # Windows:
   venv\Scripts\activate
   # Linux/Mac:
   source venv/bin/activate
   
   # 安装依赖
   pip install -r requirements.txt
   
   # 配置环境变量
   cp .env.example .env
   # 编辑.env文件，配置数据库等信息
   
   # 运行数据库迁移
   python manage.py migrate
   
   # 创建超级用户
   python manage.py createsuperuser
   
   # 启动开发服务器
   python manage.py runserver

3. 前端环境搭建
   
   # Web管理端
   cd frontend/web-admin
   npm install
   npm run dev
   
   # 微信小程序
   使用微信开发者工具打开 frontend/miniprogram 目录

3.2 生产环境部署
--------------------------------------------------------------------------------

1. 使用Docker Compose部署
   cd deploy
   docker-compose up -d

2. 访问系统
   - Web管理端: http://your-domain.com
   - API文档: http://your-domain.com/swagger/
   - 管理后台: http://your-domain.com/admin/

四、功能使用指南
================================================================================

4.1 考生用户使用指南
--------------------------------------------------------------------------------

1. 注册登录
   - 打开微信小程序"黔南州考试系统"
   - 点击"微信授权登录"
   - 完善个人信息（姓名、身份证、学历等）

2. 查看考试信息
   - 在首页查看最新考试公告
   - 点击"考试"标签查看所有考试列表
   - 点击具体考试查看详细信息和报名要求

3. 成绩查询
   - 考试结束后，在"我的"页面点击"成绩查询"
   - 查看初评成绩、考核测评成绩和总成绩
   - 查看成绩排名和合格状态

4. 岗位浏览与申请
   - 点击"岗位"标签浏览所有岗位
   - 使用搜索功能按地区、部门、学历等条件筛选
   - 点击岗位查看详细要求和薪资信息
   - 点击"申请"按钮提交岗位申请

5. 智能推荐
   - 在首页查看系统推荐的适合岗位
   - 查看推荐理由和匹配度分析
   - 收藏感兴趣的岗位

6. 准考证打印
   - 在考试详情页面点击"打印准考证"
   - 系统自动生成PDF格式准考证
   - 下载并打印准考证

4.2 管理员使用指南
--------------------------------------------------------------------------------

1. 登录管理后台
   - 访问 http://your-domain.com/admin/
   - 使用管理员账号密码登录

2. 用户管理
   - 查看所有注册用户信息
   - 审核考生资料的真实性
   - 管理用户权限和状态

3. 考试管理
   - 创建新的考试信息
   - 设置考试时间、地点、科目等
   - 管理考试报名和准考证生成
   - 录入和管理考试成绩

4. 岗位管理
   - 发布新的岗位信息
   - 编辑岗位要求和薪资待遇
   - 查看岗位申请情况
   - 管理岗位状态（开放/关闭）

5. 数据分析
   - 查看考试成绩统计图表
   - 分析岗位竞争热度
   - 导出各类统计报表

4.3 招聘单位使用指南
--------------------------------------------------------------------------------

1. 账号申请
   - 联系系统管理员申请招聘单位账号
   - 提供单位资质证明材料

2. 岗位发布
   - 登录Web管理端
   - 点击"岗位管理" -> "发布岗位"
   - 填写岗位信息、要求、薪资等
   - 提交审核

3. 简历筛选
   - 查看岗位申请人员列表
   - 查看申请人的详细简历和成绩
   - 筛选符合要求的候选人

4. 招聘进度管理
   - 更新招聘状态（初审、面试、录用等）
   - 发送面试通知
   - 管理录用结果

五、系统配置说明
================================================================================

5.1 环境变量配置
--------------------------------------------------------------------------------

在.env文件中配置以下参数：

# Django基础配置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置
DB_NAME=qiannan_exam
DB_USER=root
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# Redis配置
REDIS_URL=redis://127.0.0.1:6379/1

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

5.2 成绩计算规则
--------------------------------------------------------------------------------

总成绩计算公式：
总成绩 = 初评成绩 × 40% + 考核测评成绩 × 60%

合格标准：
- 初评成绩最低合格分数线：50分
- 考核测评成绩最低合格分数线：70分
- 两项成绩均达到合格线才能获得总成绩

5.3 推荐算法配置
--------------------------------------------------------------------------------

协同过滤推荐：
- 最少交互次数：5次
- 相似度阈值：0.1

基于内容的推荐权重：
- 学历匹配：30%
- 专业匹配：25%
- 工作经验：20%
- 工作地点：15%
- 薪资期望：10%

六、常见问题解答
================================================================================

Q1: 忘记密码怎么办？
A1: 微信小程序用户无需密码，直接微信授权登录即可。Web端用户可联系管理员重置密码。

Q2: 成绩什么时候可以查询？
A2: 考试结束后，管理员录入成绩后即可查询。具体时间请关注考试公告。

Q3: 可以申请多个岗位吗？
A3: 可以，但建议根据自身条件选择最匹配的岗位申请。

Q4: 推荐的岗位不合适怎么办？
A4: 可以完善个人信息和偏好设置，系统会根据新信息重新推荐。

Q5: 准考证打印有问题怎么办？
A5: 请检查浏览器设置，确保允许下载PDF文件。如仍有问题请联系技术支持。

Q6: 系统运行缓慢怎么办？
A6: 请检查网络连接，清除浏览器缓存，或联系技术支持。

七、技术支持
================================================================================

如遇到技术问题，请通过以下方式联系：

1. 系统内反馈：在小程序或Web端提交问题反馈
2. 邮件支持：发送邮件至 <EMAIL>
3. 电话支持：拨打技术支持热线 400-xxx-xxxx

工作时间：周一至周五 9:00-18:00

八、版本更新记录
================================================================================

v1.0.0 (2024-01-01)
- 初始版本发布
- 基础功能实现：用户管理、考试管理、岗位管理
- 微信小程序端和Web管理端

v1.1.0 (计划中)
- 增加智能推荐功能
- 优化数据可视化
- 增加消息通知功能

九、免责声明
================================================================================

1. 本系统仅用于黔南州政策性考试管理，不得用于其他商业用途。
2. 用户应确保提供信息的真实性和准确性。
3. 系统开发方不承担因用户操作不当导致的任何损失。
4. 系统功能可能因技术升级而调整，以实际使用为准。

================================================================================
                              文档结束
================================================================================
