<!--pages/login/login.wxml-->
<view class="container">
  <!-- 顶部logo和标题 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"/>
    <text class="title">黔南州考试岗位推荐系统</text>
    <text class="subtitle">智能匹配 · 精准推荐</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 微信授权登录 -->
    <view class="wechat-login">
      <van-button 
        type="primary" 
        size="large" 
        round
        loading="{{wechatLoading}}"
        loading-text="登录中..."
        bind:click="onWechatLogin"
        custom-class="wechat-btn"
      >
        <van-icon name="wechat" size="20px" color="#fff" custom-style="margin-right: 10rpx;"/>
        微信快速登录
      </van-button>
      <text class="login-tip">使用微信账号快速登录，安全便捷</text>
    </view>

    <!-- 分割线 -->
    <view class="divider">
      <view class="divider-line"></view>
      <text class="divider-text">或</text>
      <view class="divider-line"></view>
    </view>

    <!-- 账号密码登录 -->
    <view class="account-login" wx:if="{{showAccountLogin}}">
      <van-cell-group>
        <van-field
          value="{{username}}"
          placeholder="请输入用户名/手机号"
          border="{{false}}"
          bind:change="onUsernameChange"
          left-icon="manager"
          clearable
        />
        <van-field
          value="{{password}}"
          type="password"
          placeholder="请输入密码"
          border="{{false}}"
          bind:change="onPasswordChange"
          left-icon="lock"
          clearable
        />
      </van-cell-group>
      
      <van-button 
        type="primary" 
        size="large" 
        round
        loading="{{accountLoading}}"
        loading-text="登录中..."
        bind:click="onAccountLogin"
        custom-class="account-btn"
      >
        登录
      </van-button>
    </view>

    <!-- 切换登录方式 -->
    <view class="switch-login">
      <text 
        class="switch-text" 
        bind:tap="toggleLoginType"
      >
        {{showAccountLogin ? '使用微信登录' : '使用账号密码登录'}}
      </text>
    </view>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <van-checkbox 
      value="{{agreedToTerms}}" 
      bind:change="onAgreementChange"
      custom-class="agreement-checkbox"
    >
      我已阅读并同意
    </van-checkbox>
    <text class="agreement-link" bind:tap="showUserAgreement">《用户协议》</text>
    <text>和</text>
    <text class="agreement-link" bind:tap="showPrivacyPolicy">《隐私政策》</text>
  </view>

  <!-- 帮助信息 -->
  <view class="help-info">
    <view class="help-item" bind:tap="showRegisterHelp">
      <van-icon name="question-o" size="16px" color="#999"/>
      <text>如何注册账号？</text>
    </view>
    <view class="help-item" bind:tap="showLoginHelp">
      <van-icon name="info-o" size="16px" color="#999"/>
      <text>登录遇到问题？</text>
    </view>
    <view class="help-item" bind:tap="contactSupport">
      <van-icon name="phone-o" size="16px" color="#999"/>
      <text>联系客服</text>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />

<!-- 用户协议弹窗 -->
<van-popup 
  show="{{showAgreementPopup}}" 
  position="bottom" 
  custom-style="height: 70%;"
  bind:close="closeAgreementPopup"
>
  <view class="agreement-popup">
    <view class="popup-header">
      <text class="popup-title">{{agreementTitle}}</text>
      <van-icon name="cross" size="20px" bind:tap="closeAgreementPopup"/>
    </view>
    <scroll-view class="popup-content" scroll-y>
      <text class="agreement-content">{{agreementContent}}</text>
    </scroll-view>
  </view>
</van-popup>
