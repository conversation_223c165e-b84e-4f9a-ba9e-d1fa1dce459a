"""
自定义权限类
"""
from rest_framework import permissions


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    自定义权限：只有对象的所有者才能编辑它
    """
    
    def has_object_permission(self, request, view, obj):
        # 读取权限对所有请求都允许
        # 所以我们总是允许GET、HEAD或OPTIONS请求
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只给对象的所有者
        return obj.user == request.user


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    自定义权限：只有管理员才能编辑，其他用户只读
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_staff


class IsCandidateUser(permissions.BasePermission):
    """
    自定义权限：只有考生用户才能访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            hasattr(request.user, 'user_type') and 
            request.user.user_type == 'candidate'
        )


class IsRecruiterUser(permissions.BasePermission):
    """
    自定义权限：只有招聘单位用户才能访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            hasattr(request.user, 'user_type') and 
            request.user.user_type == 'recruiter'
        )


class IsAdminUser(permissions.BasePermission):
    """
    自定义权限：只有管理员用户才能访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            request.user.is_staff
        )
