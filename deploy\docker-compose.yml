version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: qiannan_mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123456}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-qiannan_exam}
      MYSQL_USER: ${MYSQL_USER:-qiannan}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-qiannan123}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./backup:/backup
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    restart: unless-stopped
    networks:
      - qiannan_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: qiannan_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # Django后端
  backend:
    build:
      context: ../backend
      dockerfile: ../deploy/Dockerfile.backend
    container_name: qiannan_backend
    environment:
      - DEBUG=False
      - DB_HOST=mysql
      - DB_NAME=qiannan_exam
      - DB_USER=qiannan
      - DB_PASSWORD=qiannan123
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  # Celery Worker
  celery:
    build:
      context: ../backend
      dockerfile: ../deploy/Dockerfile.backend
    container_name: qiannan_celery
    command: celery -A config worker -l info
    environment:
      - DEBUG=False
      - DB_HOST=mysql
      - DB_NAME=qiannan_exam
      - DB_USER=qiannan
      - DB_PASSWORD=qiannan123
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    volumes:
      - ../backend:/app
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  # Celery Beat (定时任务)
  celery-beat:
    build:
      context: ../backend
      dockerfile: ../deploy/Dockerfile.backend
    container_name: qiannan_celery_beat
    command: celery -A config beat -l info
    environment:
      - DEBUG=False
      - DB_HOST=mysql
      - DB_NAME=qiannan_exam
      - DB_USER=qiannan
      - DB_PASSWORD=qiannan123
      - REDIS_URL=redis://redis:6379/1
      - CELERY_BROKER_URL=redis://redis:6379/0
    volumes:
      - ../backend:/app
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: qiannan_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ../frontend/web-admin/dist:/var/www/web-admin
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  static_volume:
  media_volume:
