# 部署指南

## 概述

本文档详细介绍了黔南州考试岗位推荐系统的部署流程，包括开发环境、测试环境和生产环境的部署方法。

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB 可用空间
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM以上
- **存储**: 100GB SSD
- **网络**: 1Gbps

#### 生产环境配置
- **CPU**: 8核心以上
- **内存**: 16GB RAM以上
- **存储**: 500GB SSD + 数据备份存储
- **网络**: 1Gbps + 负载均衡

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+

## 环境准备

### 1. 安装Docker

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt-get update

# 安装必要的包
sudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### CentOS/RHEL
```bash
# 安装必要的包
sudo yum install -y yum-utils

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置Docker

```bash
# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令
newgrp docker

# 验证安装
docker --version
docker-compose --version
```

## 项目部署

### 1. 获取项目代码

```bash
# 克隆项目
git clone https://github.com/your-org/qiannan-exam-system.git
cd qiannan-exam-system

# 切换到指定版本（可选）
git checkout v1.0.0
```

### 2. 配置环境变量

```bash
cd deploy

# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
vim .env
```

#### 重要配置项说明

```bash
# 环境类型
ENVIRONMENT=production

# 数据库配置
MYSQL_ROOT_PASSWORD=your-strong-root-password
MYSQL_DATABASE=qiannan_exam
MYSQL_USER=qiannan_user
MYSQL_PASSWORD=your-strong-mysql-password

# Redis配置
REDIS_PASSWORD=your-strong-redis-password

# Django配置
SECRET_KEY=your-very-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 邮件配置
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# 微信小程序配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
```

### 3. SSL证书配置（生产环境）

```bash
# 创建SSL证书目录
mkdir -p ssl

# 将SSL证书文件放入ssl目录
# cert.pem - 证书文件
# key.pem - 私钥文件

# 或者使用Let's Encrypt免费证书
sudo apt-get install certbot
sudo certbot certonly --standalone -d your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
```

### 4. 部署服务

```bash
# 赋予部署脚本执行权限
chmod +x deploy.sh

# 构建镜像
./deploy.sh prod build

# 启动服务
./deploy.sh prod start

# 查看服务状态
./deploy.sh prod status
```

## 开发环境部署

### 快速启动

```bash
# 启动开发环境
./deploy.sh dev start

# 查看日志
./deploy.sh dev logs

# 访问服务
# Web管理端: http://localhost/admin
# API文档: http://localhost:8000/docs/
```

### 开发环境特性

- 自动代码重载
- 调试模式开启
- 详细错误信息
- 开发工具集成

## 生产环境部署

### 1. 安全配置

```bash
# 配置防火墙
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 禁用不必要的服务
sudo systemctl disable apache2
sudo systemctl disable nginx
```

### 2. 性能优化

```bash
# 调整系统参数
echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf
echo 'fs.file-max=65536' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 配置Docker日志轮转
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

sudo systemctl restart docker
```

### 3. 监控配置

```bash
# 启动监控服务
docker-compose -f docker-compose.yml --profile monitoring up -d

# 访问监控面板
# Prometheus: http://your-domain:9090
# Grafana: http://your-domain:3001
```

## 数据库管理

### 数据库迁移

```bash
# 执行数据库迁移
docker-compose exec backend python manage.py migrate

# 创建超级用户
docker-compose exec backend python manage.py createsuperuser

# 收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput
```

### 数据备份

```bash
# 手动备份
./deploy.sh prod backup

# 设置自动备份（crontab）
crontab -e

# 添加以下行（每天凌晨2点备份）
0 2 * * * /path/to/project/deploy/deploy.sh prod backup
```

### 数据恢复

```bash
# 恢复数据库
docker-compose exec -T mysql mysql -u root -p$MYSQL_ROOT_PASSWORD $MYSQL_DATABASE < backup/mysql_backup_20240115_020000.sql
```

## 域名和SSL配置

### 1. 域名解析

将域名解析到服务器IP地址：
- A记录: your-domain.com → 服务器IP
- CNAME记录: www.your-domain.com → your-domain.com

### 2. Nginx配置

编辑 `nginx/conf.d/default.conf`：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 其他配置...
}
```

## 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看容器日志
docker-compose logs [service_name]

# 检查容器状态
docker-compose ps

# 重启服务
docker-compose restart [service_name]
```

#### 2. 数据库连接失败

```bash
# 检查数据库容器状态
docker-compose exec mysql mysqladmin ping -h localhost

# 检查网络连接
docker network ls
docker network inspect qiannan_network
```

#### 3. 静态文件无法访问

```bash
# 重新收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput

# 检查文件权限
docker-compose exec nginx ls -la /var/www/static/
```

### 日志查看

```bash
# 查看所有服务日志
./deploy.sh prod logs

# 查看特定服务日志
./deploy.sh prod logs backend

# 实时查看日志
docker-compose logs -f backend
```

## 更新部署

### 1. 代码更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
./deploy.sh prod build

# 重启服务
./deploy.sh prod restart
```

### 2. 零停机更新

```bash
# 使用滚动更新
docker-compose up -d --no-deps backend

# 等待健康检查通过
sleep 30

# 更新其他服务
docker-compose up -d
```

## 性能监控

### 系统监控

```bash
# 查看系统资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 应用监控

- **Prometheus**: 指标收集
- **Grafana**: 数据可视化
- **日志分析**: ELK Stack（可选）

## 安全建议

1. **定期更新系统和软件包**
2. **使用强密码和密钥认证**
3. **配置防火墙规则**
4. **定期备份数据**
5. **监控系统日志**
6. **使用HTTPS加密传输**
7. **限制数据库访问权限**

## 联系支持

如遇部署问题，请联系：
- 邮箱: <EMAIL>
- 技术支持: https://support.qiannan-exam.gov.cn
