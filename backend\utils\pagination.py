"""
自定义分页器
"""
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CustomPageNumberPagination(PageNumberPagination):
    """自定义分页器"""
    
    page_size = 20  # 每页显示数量
    page_size_query_param = 'page_size'  # 每页数量参数名
    max_page_size = 100  # 最大每页数量
    page_query_param = 'page'  # 页码参数名
    
    def get_paginated_response(self, data):
        """自定义分页响应格式"""
        return Response({
            'code': 200,
            'message': '获取成功',
            'success': True,
            'data': {
                'results': data,
                'pagination': {
                    'count': self.page.paginator.count,  # 总记录数
                    'page': self.page.number,  # 当前页码
                    'page_size': self.get_page_size(self.request),  # 每页数量
                    'total_pages': self.page.paginator.num_pages,  # 总页数
                    'has_next': self.page.has_next(),  # 是否有下一页
                    'has_previous': self.page.has_previous(),  # 是否有上一页
                    'next_page': self.page.next_page_number() if self.page.has_next() else None,
                    'previous_page': self.page.previous_page_number() if self.page.has_previous() else None,
                }
            }
        })


class SmallPageNumberPagination(CustomPageNumberPagination):
    """小分页器（用于下拉选择等场景）"""
    page_size = 10
    max_page_size = 50


class LargePageNumberPagination(CustomPageNumberPagination):
    """大分页器（用于数据导出等场景）"""
    page_size = 50
    max_page_size = 200
