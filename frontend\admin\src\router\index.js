import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

// 静态路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401.vue'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  }
]

// 动态路由
export const asyncRoutes = [
  // 用户管理
  {
    path: '/users',
    component: Layout,
    redirect: '/users/list',
    name: 'Users',
    meta: {
      title: '用户管理',
      icon: 'user',
      roles: ['admin']
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/users/list.vue'),
        meta: { title: '用户列表', icon: 'user' }
      },
      {
        path: 'candidates',
        name: 'CandidateList',
        component: () => import('@/views/users/candidates.vue'),
        meta: { title: '考生管理', icon: 'user' }
      },
      {
        path: 'recruiters',
        name: 'RecruiterList',
        component: () => import('@/views/users/recruiters.vue'),
        meta: { title: '招聘单位', icon: 'office-building' }
      }
    ]
  },
  
  // 考试管理
  {
    path: '/exams',
    component: Layout,
    redirect: '/exams/list',
    name: 'Exams',
    meta: {
      title: '考试管理',
      icon: 'document',
      roles: ['admin', 'exam_admin']
    },
    children: [
      {
        path: 'list',
        name: 'ExamList',
        component: () => import('@/views/exams/list.vue'),
        meta: { title: '考试列表', icon: 'document' }
      },
      {
        path: 'create',
        name: 'ExamCreate',
        component: () => import('@/views/exams/create.vue'),
        meta: { title: '创建考试', icon: 'edit' }
      },
      {
        path: 'edit/:id',
        name: 'ExamEdit',
        component: () => import('@/views/exams/edit.vue'),
        meta: { title: '编辑考试', icon: 'edit' },
        hidden: true
      },
      {
        path: 'registrations',
        name: 'ExamRegistrations',
        component: () => import('@/views/exams/registrations.vue'),
        meta: { title: '报名管理', icon: 'user' }
      },
      {
        path: 'scores',
        name: 'ExamScores',
        component: () => import('@/views/exams/scores.vue'),
        meta: { title: '成绩管理', icon: 'data-line' }
      }
    ]
  },
  
  // 岗位管理
  {
    path: '/positions',
    component: Layout,
    redirect: '/positions/list',
    name: 'Positions',
    meta: {
      title: '岗位管理',
      icon: 'suitcase',
      roles: ['admin', 'recruiter']
    },
    children: [
      {
        path: 'categories',
        name: 'PositionCategories',
        component: () => import('@/views/positions/categories.vue'),
        meta: { title: '岗位分类', icon: 'menu' }
      },
      {
        path: 'list',
        name: 'PositionList',
        component: () => import('@/views/positions/list.vue'),
        meta: { title: '岗位列表', icon: 'suitcase' }
      },
      {
        path: 'create',
        name: 'PositionCreate',
        component: () => import('@/views/positions/create.vue'),
        meta: { title: '发布岗位', icon: 'edit' }
      },
      {
        path: 'edit/:id',
        name: 'PositionEdit',
        component: () => import('@/views/positions/edit.vue'),
        meta: { title: '编辑岗位', icon: 'edit' },
        hidden: true
      },
      {
        path: 'applications',
        name: 'PositionApplications',
        component: () => import('@/views/positions/applications.vue'),
        meta: { title: '申请管理', icon: 'user' }
      }
    ]
  },
  
  // 推荐系统
  {
    path: '/recommendations',
    component: Layout,
    redirect: '/recommendations/analytics',
    name: 'Recommendations',
    meta: {
      title: '推荐系统',
      icon: 'star',
      roles: ['admin']
    },
    children: [
      {
        path: 'analytics',
        name: 'RecommendationAnalytics',
        component: () => import('@/views/recommendations/analytics.vue'),
        meta: { title: '推荐分析', icon: 'data-analysis' }
      },
      {
        path: 'algorithms',
        name: 'RecommendationAlgorithms',
        component: () => import('@/views/recommendations/algorithms.vue'),
        meta: { title: '算法管理', icon: 'cpu' }
      },
      {
        path: 'feedback',
        name: 'RecommendationFeedback',
        component: () => import('@/views/recommendations/feedback.vue'),
        meta: { title: '用户反馈', icon: 'chat-dot-round' }
      }
    ]
  },
  
  // 数据分析
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/overview',
    name: 'Analytics',
    meta: {
      title: '数据分析',
      icon: 'data-analysis',
      roles: ['admin']
    },
    children: [
      {
        path: 'overview',
        name: 'AnalyticsOverview',
        component: () => import('@/views/analytics/overview.vue'),
        meta: { title: '数据概览', icon: 'data-board' }
      },
      {
        path: 'reports',
        name: 'AnalyticsReports',
        component: () => import('@/views/analytics/reports.vue'),
        meta: { title: '统计报表', icon: 'document' }
      },
      {
        path: 'export',
        name: 'AnalyticsExport',
        component: () => import('@/views/analytics/export.vue'),
        meta: { title: '数据导出', icon: 'download' }
      }
    ]
  },
  
  // 系统设置
  {
    path: '/system',
    component: Layout,
    redirect: '/system/settings',
    name: 'System',
    meta: {
      title: '系统管理',
      icon: 'setting',
      roles: ['admin']
    },
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/settings.vue'),
        meta: { title: '系统设置', icon: 'setting' }
      },
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/logs.vue'),
        meta: { title: '系统日志', icon: 'document' }
      },
      {
        path: 'backup',
        name: 'SystemBackup',
        component: () => import('@/views/system/backup.vue'),
        meta: { title: '数据备份', icon: 'folder' }
      }
    ]
  },
  
  // 404页面必须放在最后
  { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  scrollBehavior: () => ({ top: 0 }),
  routes: constantRoutes
})

export default router
