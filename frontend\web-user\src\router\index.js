import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Auth/Login.vue'),
    meta: { title: '登录', hideHeader: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Auth/Register.vue'),
    meta: { title: '注册', hideHeader: true }
  },
  {
    path: '/exams',
    name: 'Exams',
    component: () => import('@/views/Exams/index.vue'),
    meta: { title: '考试信息' }
  },
  {
    path: '/exams/:id',
    name: 'ExamDetail',
    component: () => import('@/views/Exams/Detail.vue'),
    meta: { title: '考试详情' }
  },
  {
    path: '/positions',
    name: 'Positions',
    component: () => import('@/views/Positions/index.vue'),
    meta: { title: '岗位信息' }
  },
  {
    path: '/positions/:id',
    name: 'PositionDetail',
    component: () => import('@/views/Positions/Detail.vue'),
    meta: { title: '岗位详情' }
  },
  {
    path: '/recommendations',
    name: 'Recommendations',
    component: () => import('@/views/Recommendations/index.vue'),
    meta: { title: '智能推荐', requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile/index.vue'),
    meta: { title: '个人中心', requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/profile/info'
      },
      {
        path: 'info',
        name: 'ProfileInfo',
        component: () => import('@/views/Profile/Info.vue'),
        meta: { title: '个人信息' }
      },
      {
        path: 'applications',
        name: 'MyApplications',
        component: () => import('@/views/Profile/Applications.vue'),
        meta: { title: '我的申请' }
      },
      {
        path: 'favorites',
        name: 'MyFavorites',
        component: () => import('@/views/Profile/Favorites.vue'),
        meta: { title: '我的收藏' }
      },
      {
        path: 'scores',
        name: 'MyScores',
        component: () => import('@/views/Profile/Scores.vue'),
        meta: { title: '我的成绩' }
      },
      {
        path: 'settings',
        name: 'ProfileSettings',
        component: () => import('@/views/Profile/Settings.vue'),
        meta: { title: '账号设置' }
      }
    ]
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/Search/index.vue'),
    meta: { title: '搜索结果' }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About/index.vue'),
    meta: { title: '关于我们' }
  },
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/Help/index.vue'),
    meta: { title: '帮助中心' }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: { title: '页面不存在' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 黔南州考试岗位推荐系统`
  }
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const token = getToken()
    
    if (!token) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查用户信息是否存在
    if (!store.getters.userInfo) {
      try {
        // 获取用户信息
        await store.dispatch('user/getUserInfo')
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 清除无效token
        store.dispatch('user/logout')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && getToken()) {
    next('/')
    return
  }
  
  next()
})

export default router
