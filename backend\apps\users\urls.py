"""
用户管理应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    UserRegistrationView, CustomTokenObtainPairView,
    UserViewSet, CandidateProfileViewSet, RecruiterProfileViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'users', UserViewSet)
router.register(r'candidate-profiles', CandidateProfileViewSet)
router.register(r'recruiter-profiles', RecruiterProfileViewSet)

urlpatterns = [
    # 认证相关
    path('auth/register/', UserRegistrationView.as_view(), name='user-register'),
    path('auth/login/', CustomTokenObtainPairView.as_view(), name='token-obtain-pair'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token-refresh'),


    # 视图集路由
    path('', include(router.urls)),
]
