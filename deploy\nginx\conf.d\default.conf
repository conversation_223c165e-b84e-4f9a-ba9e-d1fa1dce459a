# 黔南州考试岗位推荐系统站点配置

# HTTP重定向到HTTPS (生产环境)
server {
    listen 80;
    server_name _;
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 重定向到HTTPS (生产环境启用)
    # return 301 https://$server_name$request_uri;
    
    # 开发环境直接处理HTTP请求
    include /etc/nginx/conf.d/common.conf;
}

# HTTPS配置 (生产环境)
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    include /etc/nginx/conf.d/common.conf;
}
