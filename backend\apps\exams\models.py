"""
考试管理模型
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.users.models import User


class Exam(models.Model):
    """考试信息"""
    
    EXAM_TYPE_CHOICES = [
        ('civil_servant', '公务员考试'),
        ('institution', '事业单位考试'),
        ('teacher', '教师招聘考试'),
        ('other', '其他考试'),
    ]
    
    STATUS_CHOICES = [
        ('upcoming', '即将开始'),
        ('registration', '报名中'),
        ('ongoing', '进行中'),
        ('finished', '已结束'),
        ('cancelled', '已取消'),
    ]
    
    # 基本信息
    name = models.CharField('考试名称', max_length=200)
    description = models.TextField('考试描述', blank=True, null=True)
    exam_type = models.CharField('考试类型', max_length=50, choices=EXAM_TYPE_CHOICES)
    
    # 时间安排
    start_date = models.DateTimeField('考试开始时间')
    end_date = models.DateTimeField('考试结束时间')
    registration_start = models.DateTimeField('报名开始时间', blank=True, null=True)
    registration_end = models.DateTimeField('报名结束时间', blank=True, null=True)
    
    # 考试地点
    exam_location = models.CharField('考试地点', max_length=200, blank=True, null=True)
    
    # 考试要求
    requirements = models.TextField('报考要求', blank=True, null=True)
    exam_subjects = models.TextField('考试科目', blank=True, null=True)
    
    # 成绩设置
    initial_score_weight = models.DecimalField('初评成绩权重', max_digits=3, decimal_places=2, default=0.40)
    assessment_score_weight = models.DecimalField('考核测评成绩权重', max_digits=3, decimal_places=2, default=0.60)
    initial_pass_score = models.DecimalField('初评合格分数线', max_digits=5, decimal_places=2, default=50.00)
    assessment_pass_score = models.DecimalField('考核测评合格分数线', max_digits=5, decimal_places=2, default=70.00)
    
    # 状态
    status = models.CharField('考试状态', max_length=20, choices=STATUS_CHOICES, default='upcoming')
    is_published = models.BooleanField('是否发布', default=False)
    
    # 统计信息
    registration_count = models.IntegerField('报名人数', default=0)
    participant_count = models.IntegerField('参考人数', default=0)
    
    # 创建信息
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                  related_name='created_exams', verbose_name='创建人')
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'exams_exam'
        verbose_name = '考试'
        verbose_name_plural = '考试'
        ordering = ['-start_date']
    
    def __str__(self):
        return self.name
    
    @property
    def is_registration_open(self):
        """是否在报名期间"""
        now = timezone.now()
        if self.registration_start and self.registration_end:
            return self.registration_start <= now <= self.registration_end
        return False
    
    @property
    def is_ongoing(self):
        """是否正在进行"""
        now = timezone.now()
        return self.start_date <= now <= self.end_date


class ExamRegistration(models.Model):
    """考试报名"""
    
    STATUS_CHOICES = [
        ('pending', '待审核'),
        ('approved', '已通过'),
        ('rejected', '已拒绝'),
        ('cancelled', '已取消'),
    ]
    
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, verbose_name='考试', related_name='registrations')
    candidate = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='考生', related_name='exam_registrations')
    
    # 报名信息
    registration_number = models.CharField('报名号', max_length=50, unique=True)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 审核信息
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                   related_name='reviewed_registrations', verbose_name='审核人')
    reviewed_at = models.DateTimeField('审核时间', blank=True, null=True)
    review_notes = models.TextField('审核备注', blank=True, null=True)
    
    # 时间戳
    created_at = models.DateTimeField('报名时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'exams_registration'
        verbose_name = '考试报名'
        verbose_name_plural = '考试报名'
        unique_together = ['exam', 'candidate']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.candidate.display_name} - {self.exam.name}"


class AdmissionTicket(models.Model):
    """准考证"""
    
    registration = models.OneToOneField(ExamRegistration, on_delete=models.CASCADE, 
                                       verbose_name='报名记录', related_name='admission_ticket')
    
    # 准考证信息
    ticket_number = models.CharField('准考证号', max_length=50, unique=True)
    exam_room = models.CharField('考场', max_length=50, blank=True, null=True)
    seat_number = models.CharField('座位号', max_length=10, blank=True, null=True)
    exam_time = models.DateTimeField('考试时间', blank=True, null=True)
    exam_address = models.CharField('考试地址', max_length=200, blank=True, null=True)
    
    # 注意事项
    notes = models.TextField('注意事项', blank=True, null=True)
    
    # 生成信息
    generated_at = models.DateTimeField('生成时间', default=timezone.now)
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                    related_name='generated_tickets', verbose_name='生成人')
    
    class Meta:
        db_table = 'exams_admission_ticket'
        verbose_name = '准考证'
        verbose_name_plural = '准考证'
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.ticket_number} - {self.registration.candidate.display_name}"


class Score(models.Model):
    """考试成绩"""
    
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, verbose_name='考试', related_name='scores')
    candidate = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='考生', related_name='exam_scores')
    
    # 成绩信息
    initial_score = models.DecimalField('初评成绩', max_digits=5, decimal_places=2, blank=True, null=True,
                                       validators=[MinValueValidator(0), MaxValueValidator(100)])
    assessment_score = models.DecimalField('考核测评成绩', max_digits=5, decimal_places=2, blank=True, null=True,
                                          validators=[MinValueValidator(0), MaxValueValidator(100)])
    total_score = models.DecimalField('总成绩', max_digits=5, decimal_places=2, blank=True, null=True,
                                     validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    # 排名和状态
    rank = models.IntegerField('排名', blank=True, null=True)
    is_qualified = models.BooleanField('是否合格', default=False)
    
    # 各科成绩（JSON格式存储）
    subject_scores = models.JSONField('各科成绩', blank=True, null=True)
    
    # 录入信息
    entered_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                  related_name='entered_scores', verbose_name='录入人')
    entered_at = models.DateTimeField('录入时间', blank=True, null=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'exams_score'
        verbose_name = '考试成绩'
        verbose_name_plural = '考试成绩'
        unique_together = ['exam', 'candidate']
        ordering = ['-total_score', '-created_at']
    
    def __str__(self):
        return f"{self.candidate.display_name} - {self.exam.name} - {self.total_score or '未计算'}"
    
    def calculate_total_score(self):
        """计算总成绩"""
        if self.initial_score is not None and self.assessment_score is not None:
            # 检查是否达到合格线
            if (self.initial_score >= self.exam.initial_pass_score and 
                self.assessment_score >= self.exam.assessment_pass_score):
                
                # 计算加权总成绩
                total = (self.initial_score * self.exam.initial_score_weight + 
                        self.assessment_score * self.exam.assessment_score_weight)
                self.total_score = round(total, 2)
                self.is_qualified = True
            else:
                self.total_score = 0
                self.is_qualified = False
        else:
            self.total_score = None
            self.is_qualified = False
        
        return self.total_score
    
    def save(self, *args, **kwargs):
        """保存时自动计算总成绩"""
        self.calculate_total_score()
        super().save(*args, **kwargs)
