<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    console.log('黔南州考试岗位推荐系统管理端启动')
    
    // 设置页面标题
    document.title = '黔南州考试岗位推荐系统 - 管理端'
    
    // 设置favicon
    this.setFavicon()
    
    // 监听网络状态
    this.setupNetworkListener()
  },
  
  methods: {
    // 设置网站图标
    setFavicon() {
      const link = document.querySelector("link[rel*='icon']") || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = '/favicon.ico'
      document.getElementsByTagName('head')[0].appendChild(link)
    },
    
    // 监听网络状态
    setupNetworkListener() {
      window.addEventListener('online', () => {
        this.$message.success('网络连接已恢复')
      })
      
      window.addEventListener('offline', () => {
        this.$message.warning('网络连接已断开')
      })
    }
  }
}
</script>

<style lang="scss">
#app {
  height: 100vh;
  width: 100vw;
}

// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Element Plus 样式覆盖
.el-message-box {
  border-radius: 8px;
}

.el-button {
  border-radius: 4px;
}

.el-input__inner {
  border-radius: 4px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 响应式工具类
@media (max-width: 768px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
