"""
推荐系统视图
"""
import logging
from django.utils import timezone
from django.db.models import Q, Count, Avg
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from utils.response import APIResponse
from utils.permissions import IsCandidateUser
from utils.pagination import CustomPageNumberPagination
from apps.positions.models import Position
from .models import (
    UserBehavior, UserPreference, RecommendationRecord, 
    RecommendationFeedback, UserSimilarity, PositionSimilarity
)
from .serializers import (
    UserBehaviorSerializer, UserBehaviorCreateSerializer,
    UserPreferenceSerializer, UserPreferenceUpdateSerializer,
    RecommendationRecordSerializer, RecommendationFeedbackSerializer,
    RecommendationFeedbackCreateSerializer, RecommendationRequestSerializer,
    RecommendationResultSerializer
)
from .algorithms import recommendation_service

logger = logging.getLogger(__name__)


class UserBehaviorViewSet(ModelViewSet):
    """用户行为管理"""
    
    queryset = UserBehavior.objects.all()
    serializer_class = UserBehaviorSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['behavior_type']
    ordering_fields = ['created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return UserBehaviorCreateSerializer
        return UserBehaviorSerializer
    
    def get_queryset(self):
        """只能查看自己的行为记录"""
        return super().get_queryset().filter(user=self.request.user).select_related('position')
    
    def create(self, request, *args, **kwargs):
        """创建用户行为记录"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            behavior = serializer.save()
            return APIResponse.success(
                UserBehaviorSerializer(behavior).data,
                message="行为记录创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)


class UserPreferenceViewSet(ModelViewSet):
    """用户偏好管理"""
    
    queryset = UserPreference.objects.all()
    serializer_class = UserPreferenceSerializer
    permission_classes = [IsCandidateUser]
    
    def get_queryset(self):
        """只能查看自己的偏好设置"""
        return super().get_queryset().filter(user=self.request.user)
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action in ['update', 'partial_update']:
            return UserPreferenceUpdateSerializer
        return UserPreferenceSerializer
    
    def create(self, request, *args, **kwargs):
        """创建用户偏好"""
        # 检查是否已存在
        if UserPreference.objects.filter(user=request.user).exists():
            return APIResponse.error("用户偏好已存在，请使用更新接口")
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            preference = UserPreference.objects.create(
                user=request.user,
                **serializer.validated_data
            )
            return APIResponse.success(
                UserPreferenceSerializer(preference).data,
                message="偏好设置创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)
    
    def update(self, request, *args, **kwargs):
        """更新用户偏好"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                UserPreferenceSerializer(instance).data,
                message="偏好设置更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)
    
    @action(detail=False, methods=['get', 'post', 'put'])
    def my_preference(self, request):
        """获取或设置我的偏好"""
        try:
            preference = UserPreference.objects.get(user=request.user)
        except UserPreference.DoesNotExist:
            preference = None
        
        if request.method == 'GET':
            if preference:
                serializer = UserPreferenceSerializer(preference)
                return APIResponse.success(serializer.data)
            else:
                return APIResponse.success(None, message="尚未设置偏好")
        
        else:  # POST or PUT
            if preference:
                serializer = UserPreferenceUpdateSerializer(
                    preference, data=request.data, partial=True
                )
            else:
                serializer = UserPreferenceUpdateSerializer(data=request.data)
            
            if serializer.is_valid():
                if preference:
                    serializer.save()
                else:
                    preference = UserPreference.objects.create(
                        user=request.user,
                        **serializer.validated_data
                    )
                
                return APIResponse.success(
                    UserPreferenceSerializer(preference).data,
                    message="偏好设置保存成功"
                )
            return APIResponse.error("保存失败", data=serializer.errors)


class RecommendationViewSet(ModelViewSet):
    """推荐记录管理"""
    
    queryset = RecommendationRecord.objects.all()
    serializer_class = RecommendationRecordSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsCandidateUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['algorithm_type', 'status']
    ordering_fields = ['recommended_at', 'recommendation_score']
    ordering = ['-recommended_at']
    
    def get_queryset(self):
        """只能查看自己的推荐记录"""
        return super().get_queryset().filter(user=self.request.user).select_related('position')
    
    @action(detail=False, methods=['post'])
    def generate(self, request):
        """生成推荐"""
        serializer = RecommendationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.error("参数错误", data=serializer.errors)
        
        data = serializer.validated_data
        algorithm_type = data['algorithm_type']
        count = data['count']
        exclude_applied = data['exclude_applied']
        exclude_viewed = data['exclude_viewed']
        
        try:
            # 生成推荐
            recommendations = recommendation_service.get_recommendations(
                user=request.user,
                algorithm_type=algorithm_type,
                count=count,
                exclude_applied=exclude_applied,
                exclude_viewed=exclude_viewed
            )
            
            if not recommendations:
                return APIResponse.success([], message="暂无推荐结果")
            
            # 保存推荐记录
            import uuid
            batch_id = str(uuid.uuid4())
            records = recommendation_service.save_recommendations(
                user=request.user,
                recommendations=recommendations,
                batch_id=batch_id
            )
            
            # 转换为响应格式
            result_data = []
            for i, rec in enumerate(recommendations):
                result_data.append({
                    'position': rec['position'],
                    'score': rec['score'],
                    'reason': rec['reason'],
                    'algorithm_type': rec['algorithm_type'],
                    'confidence': rec.get('confidence', 0),
                    'feature_weights': rec.get('feature_weights', {}),
                    'record_id': records[i].id if i < len(records) else None
                })
            
            return APIResponse.success(result_data, message="推荐生成成功")
            
        except Exception as e:
            logger.error(f"生成推荐失败: {e}")
            return APIResponse.error("推荐生成失败，请稍后重试")
    
    @action(detail=True, methods=['post'])
    def click(self, request, pk=None):
        """点击推荐"""
        recommendation = self.get_object()
        
        # 更新推荐状态
        recommendation.status = 'clicked'
        recommendation.clicked_at = timezone.now()
        recommendation.save(update_fields=['status', 'clicked_at'])
        
        # 记录用户行为
        recommendation_service.update_user_behavior(
            user=request.user,
            position=recommendation.position,
            behavior_type='click',
            behavior_score=1.0
        )
        
        return APIResponse.success(message="点击记录成功")
    
    @action(detail=False, methods=['get'])
    def my_recommendations(self, request):
        """我的推荐"""
        recommendations = self.get_queryset().filter(
            status__in=['active', 'clicked']
        ).order_by('-recommended_at')
        
        page = self.paginate_queryset(recommendations)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(recommendations, many=True)
        return APIResponse.success(serializer.data)


class RecommendationFeedbackViewSet(ModelViewSet):
    """推荐反馈管理"""
    
    queryset = RecommendationFeedback.objects.all()
    serializer_class = RecommendationFeedbackSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsCandidateUser]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return RecommendationFeedbackCreateSerializer
        return RecommendationFeedbackSerializer
    
    def get_queryset(self):
        """只能查看自己的反馈记录"""
        return super().get_queryset().filter(user=self.request.user).select_related(
            'recommendation__position'
        )
    
    def create(self, request, *args, **kwargs):
        """创建推荐反馈"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            feedback = serializer.save()
            return APIResponse.success(
                RecommendationFeedbackSerializer(feedback).data,
                message="反馈提交成功"
            )
        return APIResponse.error("反馈提交失败", data=serializer.errors)


class RecommendationAnalyticsView(APIView):
    """推荐系统分析"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """获取推荐分析数据"""
        user = request.user
        
        # 推荐统计
        recommendation_stats = {
            'total_recommendations': RecommendationRecord.objects.filter(user=user).count(),
            'clicked_recommendations': RecommendationRecord.objects.filter(
                user=user, status='clicked'
            ).count(),
            'applied_recommendations': RecommendationRecord.objects.filter(
                user=user, status='applied'
            ).count(),
        }
        
        # 计算点击率和申请率
        if recommendation_stats['total_recommendations'] > 0:
            recommendation_stats['click_rate'] = round(
                recommendation_stats['clicked_recommendations'] / recommendation_stats['total_recommendations'] * 100, 2
            )
            recommendation_stats['application_rate'] = round(
                recommendation_stats['applied_recommendations'] / recommendation_stats['total_recommendations'] * 100, 2
            )
        else:
            recommendation_stats['click_rate'] = 0
            recommendation_stats['application_rate'] = 0
        
        # 算法效果统计
        algorithm_stats = RecommendationRecord.objects.filter(user=user).values(
            'algorithm_type'
        ).annotate(
            count=Count('id'),
            avg_score=Avg('recommendation_score'),
            click_count=Count('id', filter=Q(status='clicked')),
            apply_count=Count('id', filter=Q(status='applied'))
        )
        
        # 用户行为统计
        behavior_stats = UserBehavior.objects.filter(user=user).values(
            'behavior_type'
        ).annotate(count=Count('id'))
        
        return APIResponse.success({
            'recommendation_stats': recommendation_stats,
            'algorithm_stats': list(algorithm_stats),
            'behavior_stats': list(behavior_stats),
        })
