# 通用配置文件

# 根目录
root /var/www;
index index.html index.htm;

# 错误页面
error_page 404 /404.html;
error_page 500 502 503 504 /50x.html;

# 静态文件缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
}

# 静态文件服务
location /static/ {
    alias /var/www/static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    access_log off;
}

# 媒体文件服务
location /media/ {
    alias /var/www/media/;
    expires 30d;
    add_header Cache-Control "public";
    access_log off;
}

# 管理后台
location /admin/ {
    alias /var/www/admin/;
    try_files $uri $uri/ /admin/index.html;
    
    # 缓存控制
    location ~* \.(html)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}

# API接口代理
location /api/ {
    # 限制请求频率
    limit_req zone=api burst=20 nodelay;
    
    # 代理到后端服务
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 超时设置
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # 缓冲设置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    
    # WebSocket支持
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
}

# 登录接口特殊限制
location /api/v1/users/auth/ {
    limit_req zone=login burst=5 nodelay;
    
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# Django Admin
location /django-admin/ {
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 健康检查
location /health/ {
    proxy_pass http://backend;
    access_log off;
}

# 禁止访问敏感文件
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 禁止访问配置文件
location ~* \.(conf|ini|log|bak|backup|sql)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 默认首页
location / {
    try_files $uri $uri/ /index.html;
}

# 安全头设置
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
