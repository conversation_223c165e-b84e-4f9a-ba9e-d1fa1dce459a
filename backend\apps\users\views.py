"""
用户管理视图
"""
import logging
from django.contrib.auth import authenticate
from django.utils import timezone
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView

from utils.response import APIResponse
from utils.wechat import wechat_api, WeChatAPIError
from utils.permissions import IsOwnerOrReadOnly
from .models import User, CandidateProfile, RecruiterProfile
from .serializers import (
    UserSerializer, UserRegistrationSerializer, WeChatLoginSerializer,
    PasswordChangeSerializer, CandidateProfileSerializer, CandidateProfileCreateSerializer,
    RecruiterProfileSerializer, RecruiterProfileCreateSerializer,
    UserProfileSerializer, UserUpdateSerializer
)

logger = logging.getLogger(__name__)


class UserRegistrationView(APIView):
    """用户注册"""
    
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """用户注册"""
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            
            return APIResponse.success({
                'user': UserSerializer(user).data,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
            }, message="注册成功")
        
        return APIResponse.error("注册失败", data=serializer.errors)


class WeChatLoginView(APIView):
    """微信登录"""
    
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """微信小程序登录"""
        serializer = WeChatLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.error("参数错误", data=serializer.errors)
        
        code = serializer.validated_data['code']
        user_info = serializer.validated_data.get('user_info', {})
        
        try:
            # 通过code获取openid和session_key
            wechat_data = wechat_api.code_to_session(code)
            openid = wechat_data.get('openid')
            session_key = wechat_data.get('session_key')
            unionid = wechat_data.get('unionid')
            
            if not openid:
                return APIResponse.error("微信授权失败")
            
            # 查找或创建用户
            user, created = User.objects.get_or_create(
                wechat_openid=openid,
                defaults={
                    'username': f"wx_{openid[:20]}",
                    'nickname': user_info.get('nickName', ''),
                    'wechat_unionid': unionid,
                    'user_type': 'candidate',
                    'is_active': True,
                }
            )
            
            # 更新用户信息
            if not created and user_info:
                if user_info.get('nickName') and not user.nickname:
                    user.nickname = user_info['nickName']
                if user_info.get('avatarUrl') and not user.avatar:
                    # 这里可以下载并保存头像
                    pass
                user.last_login = timezone.now()
                user.save()
            
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            
            return APIResponse.success({
                'user': UserProfileSerializer(user).data,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh),
                'session_key': session_key,  # 前端可能需要用于解密其他数据
                'is_new_user': created,
            }, message="登录成功")
            
        except WeChatAPIError as e:
            logger.error(f"微信登录失败: {e}")
            return APIResponse.error(f"微信登录失败: {e.message}")
        except Exception as e:
            logger.error(f"微信登录异常: {e}")
            return APIResponse.error("登录失败，请稍后重试")


class CustomTokenObtainPairView(TokenObtainPairView):
    """自定义JWT登录"""
    
    def post(self, request, *args, **kwargs):
        """用户名密码登录"""
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            # 获取用户信息
            username = request.data.get('username')
            user = User.objects.filter(username=username).first()
            
            if user:
                user.last_login = timezone.now()
                user.save()
                
                # 返回统一格式
                return APIResponse.success({
                    'user': UserProfileSerializer(user).data,
                    'access_token': response.data['access'],
                    'refresh_token': response.data['refresh'],
                }, message="登录成功")
        
        return APIResponse.error("用户名或密码错误")


class UserViewSet(ModelViewSet):
    """用户管理"""
    
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    
    def get_queryset(self):
        """只能查看自己的信息"""
        if self.request.user.is_staff:
            return super().get_queryset()
        return User.objects.filter(id=self.request.user.id)
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'update' or self.action == 'partial_update':
            return UserUpdateSerializer
        elif self.action == 'retrieve' or self.action == 'profile':
            return UserProfileSerializer
        return UserSerializer
    
    @action(detail=False, methods=['get', 'put', 'patch'])
    def profile(self, request):
        """获取或更新个人信息"""
        user = request.user
        
        if request.method == 'GET':
            serializer = UserProfileSerializer(user)
            return APIResponse.success(serializer.data)
        
        else:  # PUT or PATCH
            serializer = UserUpdateSerializer(user, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return APIResponse.success(
                    UserProfileSerializer(user).data,
                    message="个人信息更新成功"
                )
            return APIResponse.error("更新失败", data=serializer.errors)
    
    @action(detail=False, methods=['post'])
    def change_password(self, request):
        """修改密码"""
        serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(message="密码修改成功")
        return APIResponse.error("密码修改失败", data=serializer.errors)
    
    @action(detail=False, methods=['post'])
    def upload_avatar(self, request):
        """上传头像"""
        if 'avatar' not in request.FILES:
            return APIResponse.error("请选择头像文件")
        
        user = request.user
        user.avatar = request.FILES['avatar']
        user.save()
        
        return APIResponse.success({
            'avatar_url': user.avatar.url if user.avatar else None
        }, message="头像上传成功")


class CandidateProfileViewSet(ModelViewSet):
    """考生信息管理"""
    
    queryset = CandidateProfile.objects.all()
    serializer_class = CandidateProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """只能查看自己的信息"""
        if self.request.user.is_staff:
            return super().get_queryset()
        return CandidateProfile.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return CandidateProfileCreateSerializer
        return CandidateProfileSerializer
    
    def create(self, request, *args, **kwargs):
        """创建考生信息"""
        # 检查是否已存在
        if hasattr(request.user, 'candidate_profile'):
            return APIResponse.error("考生信息已存在，请使用更新接口")
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            profile = serializer.save()
            return APIResponse.success(
                CandidateProfileSerializer(profile).data,
                message="考生信息创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)
    
    def update(self, request, *args, **kwargs):
        """更新考生信息"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                serializer.data,
                message="考生信息更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)


class RecruiterProfileViewSet(ModelViewSet):
    """招聘单位信息管理"""
    
    queryset = RecruiterProfile.objects.all()
    serializer_class = RecruiterProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """只能查看自己的信息"""
        if self.request.user.is_staff:
            return super().get_queryset()
        return RecruiterProfile.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return RecruiterProfileCreateSerializer
        return RecruiterProfileSerializer
    
    def create(self, request, *args, **kwargs):
        """创建招聘单位信息"""
        # 检查是否已存在
        if hasattr(request.user, 'recruiter_profile'):
            return APIResponse.error("招聘单位信息已存在，请使用更新接口")
        
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            profile = serializer.save()
            return APIResponse.success(
                RecruiterProfileSerializer(profile).data,
                message="招聘单位信息创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)
    
    def update(self, request, *args, **kwargs):
        """更新招聘单位信息"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                serializer.data,
                message="招聘单位信息更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)
