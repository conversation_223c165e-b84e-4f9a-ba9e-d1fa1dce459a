{"name": "qiannan-exam-web-admin", "version": "1.0.0", "description": "黔南州考试系统Web管理端", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}, "engines": {"node": ">=16.0.0"}}