#!/bin/bash

# 黔南州考试岗位推荐系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev/staging/prod
# 操作: build/start/stop/restart/logs/backup

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 [环境] [操作]"
    log_info "环境: dev/staging/prod"
    log_info "操作: build/start/stop/restart/logs/backup/clean"
    exit 1
fi

ENVIRONMENT=$1
OPERATION=$2

# 验证环境参数
case $ENVIRONMENT in
    dev|staging|prod)
        log_info "部署环境: $ENVIRONMENT"
        ;;
    *)
        log_error "无效的环境参数: $ENVIRONMENT"
        log_info "支持的环境: dev/staging/prod"
        exit 1
        ;;
esac

# 设置环境变量文件
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "环境变量文件 $ENV_FILE 不存在，使用默认配置"
    ENV_FILE=".env.example"
fi

# Docker Compose 文件
COMPOSE_FILE="docker-compose.yml"
if [ "$ENVIRONMENT" != "prod" ]; then
    COMPOSE_FILE="docker-compose.$ENVIRONMENT.yml"
    if [ ! -f "$COMPOSE_FILE" ]; then
        COMPOSE_FILE="docker-compose.yml"
    fi
fi

# 项目名称
PROJECT_NAME="qiannan-exam-$ENVIRONMENT"

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME --env-file $ENV_FILE build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建必要的目录
    mkdir -p logs backup mysql/init redis ssl
    
    # 启动服务
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME --env-file $ENV_FILE up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
    
    log_success "服务重启完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查容器状态
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps
    
    # 检查健康状态
    log_info "检查服务健康状态..."
    
    # 检查数据库
    if docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec -T mysql mysqladmin ping -h localhost --silent; then
        log_success "MySQL 服务正常"
    else
        log_error "MySQL 服务异常"
    fi
    
    # 检查Redis
    if docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec -T redis redis-cli ping | grep -q PONG; then
        log_success "Redis 服务正常"
    else
        log_error "Redis 服务异常"
    fi
    
    # 检查后端API
    if curl -f http://localhost:8000/health/ &> /dev/null; then
        log_success "后端API服务正常"
    else
        log_warning "后端API服务可能异常"
    fi
}

# 查看日志
view_logs() {
    log_info "查看服务日志..."
    
    if [ $# -gt 2 ]; then
        SERVICE=$3
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f $SERVICE
    else
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f
    fi
}

# 数据库备份
backup_database() {
    log_info "备份数据库..."
    
    BACKUP_DIR="backup"
    BACKUP_FILE="$BACKUP_DIR/mysql_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p $BACKUP_DIR
    
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec -T mysql mysqldump \
        -u root -p$MYSQL_ROOT_PASSWORD $MYSQL_DATABASE > $BACKUP_FILE
    
    # 压缩备份文件
    gzip $BACKUP_FILE
    
    log_success "数据库备份完成: $BACKUP_FILE.gz"
    
    # 清理旧备份 (保留最近7天)
    find $BACKUP_DIR -name "mysql_backup_*.sql.gz" -mtime +7 -delete
    
    log_info "已清理7天前的备份文件"
}

# 清理资源
clean_resources() {
    log_info "清理Docker资源..."
    
    # 停止服务
    stop_services
    
    # 删除容器
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME rm -f
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷 (谨慎使用)
    if [ "$ENVIRONMENT" = "dev" ]; then
        docker volume prune -f
    fi
    
    log_success "资源清理完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待数据库启动
    sleep 15
    
    # 运行数据库迁移
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec backend python manage.py migrate
    
    # 创建超级用户 (仅开发环境)
    if [ "$ENVIRONMENT" = "dev" ]; then
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('超级用户已创建: admin/admin123456')
"
    fi
    
    # 加载初始数据
    if [ -f "fixtures/initial_data.json" ]; then
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME exec backend python manage.py loaddata fixtures/initial_data.json
        log_success "初始数据加载完成"
    fi
    
    log_success "数据库初始化完成"
}

# 主逻辑
main() {
    check_dependencies
    
    case $OPERATION in
        build)
            build_images
            ;;
        start)
            start_services
            if [ "$ENVIRONMENT" = "dev" ]; then
                init_database
            fi
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            view_logs $@
            ;;
        backup)
            backup_database
            ;;
        clean)
            clean_resources
            ;;
        status)
            check_services
            ;;
        init-db)
            init_database
            ;;
        *)
            log_error "无效的操作: $OPERATION"
            log_info "支持的操作: build/start/stop/restart/logs/backup/clean/status/init-db"
            exit 1
            ;;
    esac
}

# 执行主函数
main $@
