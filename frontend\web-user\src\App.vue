<template>
  <div id="app">
    <!-- 顶部导航栏 -->
    <Header v-if="showHeader" />
    
    <!-- 主要内容区域 -->
    <main class="main-content" :class="{ 'with-header': showHeader }">
      <router-view />
    </main>
    
    <!-- 底部信息 -->
    <Footer v-if="showFooter" />
    
    <!-- 回到顶部按钮 -->
    <el-backtop :right="30" :bottom="30" />
  </div>
</template>

<script>
import Header from '@/components/Layout/Header.vue'
import Footer from '@/components/Layout/Footer.vue'

export default {
  name: 'App',
  components: {
    Header,
    Footer
  },
  
  computed: {
    showHeader() {
      // 在某些页面隐藏头部导航
      const hideHeaderRoutes = ['/login', '/register']
      return !hideHeaderRoutes.includes(this.$route.path)
    },
    
    showFooter() {
      // 在某些页面隐藏底部
      const hideFooterRoutes = ['/login', '/register']
      return !hideFooterRoutes.includes(this.$route.path)
    }
  },
  
  mounted() {
    console.log('黔南州考试岗位推荐系统用户端启动')
    
    // 设置页面标题
    document.title = '黔南州考试岗位推荐系统'
    
    // 设置favicon
    this.setFavicon()
    
    // 监听网络状态
    this.setupNetworkListener()
  },
  
  methods: {
    // 设置网站图标
    setFavicon() {
      const link = document.querySelector("link[rel*='icon']") || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'shortcut icon'
      link.href = '/favicon.ico'
      document.getElementsByTagName('head')[0].appendChild(link)
    },
    
    // 监听网络状态
    setupNetworkListener() {
      window.addEventListener('online', () => {
        this.$message.success('网络连接已恢复')
      })
      
      window.addEventListener('offline', () => {
        this.$message.warning('网络连接已断开')
      })
    }
  }
}
</script>

<style lang="scss">
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  
  &.with-header {
    padding-top: 60px; // 为固定头部留出空间
  }
}

// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Element Plus 样式覆盖
.el-message-box {
  border-radius: 8px;
}

.el-button {
  border-radius: 4px;
}

.el-input__inner {
  border-radius: 4px;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 容器样式
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.container-fluid {
  width: 100%;
  padding: 0 20px;
}

// 响应式工具类
@media (max-width: 768px) {
  .hidden-xs {
    display: none !important;
  }
  
  .container,
  .container-fluid {
    padding: 0 15px;
  }
}

@media (min-width: 769px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
