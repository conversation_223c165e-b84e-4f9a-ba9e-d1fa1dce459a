"""
统一响应格式工具
"""
from rest_framework.response import Response
from rest_framework import status


class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data=None, message="操作成功", code=200):
        """成功响应"""
        return Response({
            'code': code,
            'message': message,
            'data': data,
            'success': True
        }, status=status.HTTP_200_OK)
    
    @staticmethod
    def error(message="操作失败", code=400, data=None):
        """错误响应"""
        return Response({
            'code': code,
            'message': message,
            'data': data,
            'success': False
        }, status=status.HTTP_400_BAD_REQUEST)
    
    @staticmethod
    def unauthorized(message="未授权访问", code=401):
        """未授权响应"""
        return Response({
            'code': code,
            'message': message,
            'data': None,
            'success': False
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    @staticmethod
    def forbidden(message="禁止访问", code=403):
        """禁止访问响应"""
        return Response({
            'code': code,
            'message': message,
            'data': None,
            'success': False
        }, status=status.HTTP_403_FORBIDDEN)
    
    @staticmethod
    def not_found(message="资源不存在", code=404):
        """资源不存在响应"""
        return Response({
            'code': code,
            'message': message,
            'data': None,
            'success': False
        }, status=status.HTTP_404_NOT_FOUND)
    
    @staticmethod
    def server_error(message="服务器内部错误", code=500):
        """服务器错误响应"""
        return Response({
            'code': code,
            'message': message,
            'data': None,
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
