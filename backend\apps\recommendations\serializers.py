"""
推荐系统序列化器
"""
from rest_framework import serializers
from apps.users.serializers import UserSerializer
from apps.positions.serializers import PositionListSerializer
from .models import (
    UserBehavior, UserPreference, RecommendationRecord, UserSimilarity,
    PositionSimilarity, RecommendationFeedback
)


class UserBehaviorSerializer(serializers.ModelSerializer):
    """用户行为序列化器"""
    
    user = UserSerializer(read_only=True)
    position = PositionListSerializer(read_only=True)
    
    class Meta:
        model = UserBehavior
        fields = [
            'id', 'user', 'position', 'behavior_type', 'behavior_score',
            'session_id', 'ip_address', 'user_agent', 'referrer',
            'extra_data', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserBehaviorCreateSerializer(serializers.ModelSerializer):
    """用户行为创建序列化器"""
    
    position_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = UserBehavior
        fields = [
            'position_id', 'behavior_type', 'behavior_score',
            'session_id', 'extra_data'
        ]
    
    def validate_position_id(self, value):
        """验证岗位ID"""
        from apps.positions.models import Position
        try:
            position = Position.objects.get(id=value)
            return position
        except Position.DoesNotExist:
            raise serializers.ValidationError("岗位不存在")
    
    def create(self, validated_data):
        """创建用户行为记录"""
        position = validated_data.pop('position_id')
        user = self.context['request'].user
        request = self.context['request']
        
        # 获取请求信息
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        referrer = request.META.get('HTTP_REFERER', '')
        
        behavior = UserBehavior.objects.create(
            user=user,
            position=position,
            ip_address=ip_address,
            user_agent=user_agent,
            referrer=referrer,
            **validated_data
        )
        
        return behavior
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserPreferenceSerializer(serializers.ModelSerializer):
    """用户偏好序列化器"""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = UserPreference
        fields = [
            'id', 'user', 'preferred_provinces', 'preferred_cities',
            'preferred_categories', 'preferred_departments', 'min_salary',
            'max_salary', 'preferred_work_types', 'enable_recommendations',
            'recommendation_frequency', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class UserPreferenceUpdateSerializer(serializers.ModelSerializer):
    """用户偏好更新序列化器"""
    
    class Meta:
        model = UserPreference
        fields = [
            'preferred_provinces', 'preferred_cities', 'preferred_categories',
            'preferred_departments', 'min_salary', 'max_salary',
            'preferred_work_types', 'enable_recommendations',
            'recommendation_frequency'
        ]
    
    def validate(self, attrs):
        """验证偏好数据"""
        min_salary = attrs.get('min_salary')
        max_salary = attrs.get('max_salary')
        
        if min_salary and max_salary and min_salary > max_salary:
            raise serializers.ValidationError("最低期望薪资不能大于最高期望薪资")
        
        return attrs


class RecommendationRecordSerializer(serializers.ModelSerializer):
    """推荐记录序列化器"""
    
    user = UserSerializer(read_only=True)
    position = PositionListSerializer(read_only=True)
    
    class Meta:
        model = RecommendationRecord
        fields = [
            'id', 'user', 'position', 'algorithm_type', 'recommendation_score',
            'confidence_score', 'reason', 'feature_weights', 'status',
            'user_rating', 'user_feedback', 'batch_id', 'recommended_at',
            'clicked_at', 'expires_at'
        ]
        read_only_fields = [
            'id', 'recommended_at', 'clicked_at'
        ]


class RecommendationFeedbackSerializer(serializers.ModelSerializer):
    """推荐反馈序列化器"""
    
    recommendation = RecommendationRecordSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = RecommendationFeedback
        fields = [
            'id', 'recommendation', 'user', 'feedback_type', 'rating',
            'comment', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class RecommendationFeedbackCreateSerializer(serializers.ModelSerializer):
    """推荐反馈创建序列化器"""
    
    recommendation_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = RecommendationFeedback
        fields = ['recommendation_id', 'feedback_type', 'rating', 'comment']
    
    def validate_recommendation_id(self, value):
        """验证推荐记录ID"""
        try:
            recommendation = RecommendationRecord.objects.get(id=value)
            
            # 检查是否是当前用户的推荐
            user = self.context['request'].user
            if recommendation.user != user:
                raise serializers.ValidationError("无权对此推荐进行反馈")
            
            return recommendation
        except RecommendationRecord.DoesNotExist:
            raise serializers.ValidationError("推荐记录不存在")
    
    def validate(self, attrs):
        """验证反馈数据"""
        feedback_type = attrs.get('feedback_type')
        rating = attrs.get('rating')
        
        # 如果是喜欢类型的反馈，评分应该较高
        if feedback_type == 'like' and rating and rating < 3:
            raise serializers.ValidationError("喜欢类型的反馈评分应该不低于3分")
        
        # 如果是不喜欢类型的反馈，评分应该较低
        if feedback_type == 'dislike' and rating and rating > 2:
            raise serializers.ValidationError("不喜欢类型的反馈评分应该不高于2分")
        
        return attrs
    
    def create(self, validated_data):
        """创建推荐反馈"""
        recommendation = validated_data.pop('recommendation_id')
        user = self.context['request'].user
        
        # 检查是否已经反馈过
        if RecommendationFeedback.objects.filter(
            recommendation=recommendation, user=user
        ).exists():
            raise serializers.ValidationError("已经对此推荐进行过反馈")
        
        feedback = RecommendationFeedback.objects.create(
            recommendation=recommendation,
            user=user,
            **validated_data
        )
        
        # 更新推荐记录的用户评分
        if validated_data.get('rating'):
            recommendation.user_rating = validated_data['rating']
            recommendation.user_feedback = validated_data.get('comment', '')
            recommendation.save(update_fields=['user_rating', 'user_feedback'])
        
        return feedback


class UserSimilaritySerializer(serializers.ModelSerializer):
    """用户相似度序列化器"""
    
    user1 = UserSerializer(read_only=True)
    user2 = UserSerializer(read_only=True)
    
    class Meta:
        model = UserSimilarity
        fields = [
            'id', 'user1', 'user2', 'similarity_score', 'algorithm_type',
            'calculated_at', 'data_version'
        ]
        read_only_fields = ['id', 'calculated_at']


class PositionSimilaritySerializer(serializers.ModelSerializer):
    """岗位相似度序列化器"""
    
    position1 = PositionListSerializer(read_only=True)
    position2 = PositionListSerializer(read_only=True)
    
    class Meta:
        model = PositionSimilarity
        fields = [
            'id', 'position1', 'position2', 'similarity_score', 'algorithm_type',
            'feature_similarities', 'calculated_at', 'data_version'
        ]
        read_only_fields = ['id', 'calculated_at']


class RecommendationRequestSerializer(serializers.Serializer):
    """推荐请求序列化器"""
    
    algorithm_type = serializers.ChoiceField(
        choices=['collaborative_filtering', 'content_based', 'hybrid', 'popularity'],
        default='hybrid',
        help_text="推荐算法类型"
    )
    count = serializers.IntegerField(
        default=10,
        min_value=1,
        max_value=50,
        help_text="推荐数量"
    )
    exclude_applied = serializers.BooleanField(
        default=True,
        help_text="是否排除已申请的岗位"
    )
    exclude_viewed = serializers.BooleanField(
        default=False,
        help_text="是否排除已浏览的岗位"
    )


class RecommendationResultSerializer(serializers.Serializer):
    """推荐结果序列化器"""
    
    position = PositionListSerializer(read_only=True)
    score = serializers.FloatField(help_text="推荐分数")
    reason = serializers.CharField(help_text="推荐理由")
    algorithm_type = serializers.CharField(help_text="推荐算法")
    confidence = serializers.FloatField(help_text="置信度")
    feature_weights = serializers.DictField(help_text="特征权重")
