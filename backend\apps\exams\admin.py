"""
考试管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils import timezone
from .models import Exam, ExamRegistration, AdmissionTicket, Score


@admin.register(Exam)
class ExamAdmin(admin.ModelAdmin):
    """考试管理"""
    
    list_display = ['name', 'exam_type', 'status', 'start_date', 'registration_count', 'participant_count', 'is_published']
    list_filter = ['exam_type', 'status', 'is_published', 'start_date']
    search_fields = ['name', 'description']
    ordering = ['-start_date']
    date_hierarchy = 'start_date'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['name', 'description', 'exam_type']
        }),
        ('时间安排', {
            'fields': ['start_date', 'end_date', 'registration_start', 'registration_end']
        }),
        ('考试设置', {
            'fields': ['exam_location', 'requirements', 'exam_subjects']
        }),
        ('成绩设置', {
            'fields': ['initial_score_weight', 'assessment_score_weight', 'initial_pass_score', 'assessment_pass_score']
        }),
        ('状态设置', {
            'fields': ['status', 'is_published']
        }),
        ('统计信息', {
            'fields': ['registration_count', 'participant_count'],
            'classes': ['collapse']
        }),
        ('创建信息', {
            'fields': ['created_by', 'created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['registration_count', 'participant_count', 'created_at', 'updated_at']
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(ExamRegistration)
class ExamRegistrationAdmin(admin.ModelAdmin):
    """考试报名管理"""
    
    list_display = ['registration_number', 'exam', 'candidate_name', 'status', 'created_at']
    list_filter = ['status', 'exam', 'created_at']
    search_fields = ['registration_number', 'candidate__username', 'candidate__real_name']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = [
        ('报名信息', {
            'fields': ['exam', 'candidate', 'registration_number', 'status']
        }),
        ('审核信息', {
            'fields': ['reviewed_by', 'reviewed_at', 'review_notes']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at', 'reviewed_at']
    
    def candidate_name(self, obj):
        return obj.candidate.display_name
    candidate_name.short_description = '考生姓名'
    
    def save_model(self, request, obj, form, change):
        if 'status' in form.changed_data and obj.status in ['approved', 'rejected']:
            obj.reviewed_by = request.user
            obj.reviewed_at = timezone.now()
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('exam', 'candidate', 'reviewed_by')


@admin.register(AdmissionTicket)
class AdmissionTicketAdmin(admin.ModelAdmin):
    """准考证管理"""
    
    list_display = ['ticket_number', 'exam_name', 'candidate_name', 'exam_room', 'seat_number', 'generated_at']
    list_filter = ['registration__exam', 'generated_at']
    search_fields = ['ticket_number', 'registration__candidate__username', 'registration__candidate__real_name']
    ordering = ['-generated_at']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['registration', 'ticket_number']
        }),
        ('考试安排', {
            'fields': ['exam_room', 'seat_number', 'exam_time', 'exam_address']
        }),
        ('注意事项', {
            'fields': ['notes']
        }),
        ('生成信息', {
            'fields': ['generated_by', 'generated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['generated_at']
    
    def exam_name(self, obj):
        return obj.registration.exam.name
    exam_name.short_description = '考试名称'
    
    def candidate_name(self, obj):
        return obj.registration.candidate.display_name
    candidate_name.short_description = '考生姓名'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.generated_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('registration__exam', 'registration__candidate', 'generated_by')


@admin.register(Score)
class ScoreAdmin(admin.ModelAdmin):
    """考试成绩管理"""
    
    list_display = ['exam', 'candidate_name', 'initial_score', 'assessment_score', 'total_score', 'rank', 'is_qualified']
    list_filter = ['exam', 'is_qualified', 'entered_at']
    search_fields = ['candidate__username', 'candidate__real_name']
    ordering = ['exam', '-total_score']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['exam', 'candidate']
        }),
        ('成绩信息', {
            'fields': ['initial_score', 'assessment_score', 'total_score', 'rank', 'is_qualified']
        }),
        ('各科成绩', {
            'fields': ['subject_scores'],
            'classes': ['collapse']
        }),
        ('录入信息', {
            'fields': ['entered_by', 'entered_at'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['total_score', 'is_qualified', 'created_at', 'updated_at', 'entered_at']
    
    def candidate_name(self, obj):
        return obj.candidate.display_name
    candidate_name.short_description = '考生姓名'
    
    def save_model(self, request, obj, form, change):
        if 'initial_score' in form.changed_data or 'assessment_score' in form.changed_data:
            obj.entered_by = request.user
            obj.entered_at = timezone.now()
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('exam', 'candidate', 'entered_by')
    
    actions = ['calculate_ranks']
    
    def calculate_ranks(self, request, queryset):
        """批量计算排名"""
        exams = set(queryset.values_list('exam', flat=True))
        for exam_id in exams:
            scores = Score.objects.filter(exam_id=exam_id, is_qualified=True).order_by('-total_score')
            for rank, score in enumerate(scores, 1):
                score.rank = rank
                score.save(update_fields=['rank'])
        self.message_user(request, f"已更新 {len(exams)} 个考试的排名")
    calculate_ranks.short_description = "计算排名"
