"""
JWT认证相关工具
"""
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from apps.users.serializers import UserProfileSerializer


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """自定义JWT令牌序列化器"""
    
    @classmethod
    def get_token(cls, user):
        """获取令牌并添加自定义声明"""
        token = super().get_token(user)
        
        # 添加自定义声明
        token['user_id'] = user.id
        token['username'] = user.username
        token['user_type'] = user.user_type
        token['nickname'] = user.nickname or user.username
        
        return token
    
    def validate(self, attrs):
        """验证并返回令牌数据"""
        data = super().validate(attrs)
        
        # 添加用户信息
        data['user'] = UserProfileSerializer(self.user).data
        
        return data


class CustomTokenObtainPairView(TokenObtainPairView):
    """自定义JWT令牌获取视图"""
    serializer_class = CustomTokenObtainPairSerializer
