/* pages/index/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner-swiper {
  width: 100%;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 快捷入口样式 */
.quick-entry {
  display: flex;
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.entry-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.entry-item text {
  font-size: 24rpx;
  color: #333;
  margin-top: 10rpx;
}

/* 通用section样式 */
.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #1976D2;
}

/* 考试列表样式 */
.exam-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.exam-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #1976D2;
}

.exam-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.exam-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.exam-time,
.exam-location {
  font-size: 24rpx;
  color: #666;
}

.exam-status {
  margin-left: 20rpx;
}

/* 岗位列表样式 */
.position-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.position-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #4CAF50;
}

.position-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.position-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.position-department,
.position-location {
  font-size: 24rpx;
  color: #666;
}

.position-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10rpx;
}

.position-salary {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: bold;
}

.position-count {
  font-size: 24rpx;
  color: #666;
}

/* 推荐列表样式 */
.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recommendation-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #FF9800;
}

.recommendation-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.recommendation-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.recommendation-reason {
  font-size: 24rpx;
  color: #666;
}

.recommendation-score {
  font-size: 24rpx;
  color: #FF9800;
  font-weight: bold;
}

.recommendation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recommendation-department,
.recommendation-location {
  font-size: 24rpx;
  color: #666;
}

/* 公告列表样式 */
.notice-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.notice-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notice-time {
  font-size: 22rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .section {
    padding: 20rpx 15rpx;
  }
  
  .exam-item,
  .position-item,
  .recommendation-item {
    padding: 15rpx;
  }
  
  .section-title {
    font-size: 30rpx;
  }
}
