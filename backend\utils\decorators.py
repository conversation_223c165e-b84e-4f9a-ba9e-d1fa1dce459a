"""
装饰器工具
"""
from functools import wraps
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import permission_classes
from rest_framework.permissions import IsAuthenticated
from .response import APIResponse


def require_user_type(*user_types):
    """
    要求特定用户类型的装饰器
    
    Args:
        user_types: 允许的用户类型列表
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'code': 401,
                    'message': '请先登录',
                    'success': False,
                    'data': None
                }, status=401)
            
            if request.user.user_type not in user_types:
                return JsonResponse({
                    'code': 403,
                    'message': '权限不足',
                    'success': False,
                    'data': None
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_candidate(view_func):
    """要求考生用户的装饰器"""
    return require_user_type('candidate')(view_func)


def require_recruiter(view_func):
    """要求招聘单位用户的装饰器"""
    return require_user_type('recruiter')(view_func)


def require_admin(view_func):
    """要求管理员用户的装饰器"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'code': 401,
                'message': '请先登录',
                'success': False,
                'data': None
            }, status=401)
        
        if not request.user.is_staff:
            return JsonResponse({
                'code': 403,
                'message': '需要管理员权限',
                'success': False,
                'data': None
            }, status=403)
        
        return view_func(request, *args, **kwargs)
    return wrapper


def api_exception_handler(view_func):
    """API异常处理装饰器"""
    @wraps(view_func)
    def wrapper(*args, **kwargs):
        try:
            return view_func(*args, **kwargs)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"API异常: {e}", exc_info=True)
            
            return JsonResponse({
                'code': 500,
                'message': '服务器内部错误',
                'success': False,
                'data': None
            }, status=500)
    return wrapper


def rate_limit(key_func=None, rate='100/h'):
    """
    API限流装饰器
    
    Args:
        key_func: 生成限流键的函数
        rate: 限流速率，格式如 '100/h', '10/m', '1/s'
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            from django_ratelimit.decorators import ratelimit
            
            # 默认使用IP地址作为限流键
            if key_func is None:
                key = lambda group, request: request.META.get('REMOTE_ADDR')
            else:
                key = key_func
            
            # 应用限流
            limited_view = ratelimit(key=key, rate=rate, method='ALL')(view_func)
            
            return limited_view(request, *args, **kwargs)
        return wrapper
    return decorator


def log_user_action(action_name):
    """
    记录用户操作的装饰器
    
    Args:
        action_name: 操作名称
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            import logging
            logger = logging.getLogger('user_actions')
            
            user_id = getattr(request.user, 'id', None) if request.user.is_authenticated else None
            ip_address = request.META.get('REMOTE_ADDR')
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # 记录操作开始
            logger.info(f"用户操作开始: {action_name}", extra={
                'user_id': user_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'action': action_name,
                'method': request.method,
                'path': request.path,
            })
            
            try:
                result = view_func(request, *args, **kwargs)
                
                # 记录操作成功
                logger.info(f"用户操作成功: {action_name}", extra={
                    'user_id': user_id,
                    'action': action_name,
                    'status': 'success',
                })
                
                return result
                
            except Exception as e:
                # 记录操作失败
                logger.error(f"用户操作失败: {action_name}", extra={
                    'user_id': user_id,
                    'action': action_name,
                    'status': 'error',
                    'error': str(e),
                })
                raise
                
        return wrapper
    return decorator


def validate_json_data(required_fields=None):
    """
    验证JSON数据的装饰器
    
    Args:
        required_fields: 必需字段列表
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            import json
            
            # 检查Content-Type
            content_type = request.META.get('CONTENT_TYPE', '')
            if 'application/json' not in content_type:
                return JsonResponse({
                    'code': 400,
                    'message': 'Content-Type必须为application/json',
                    'success': False,
                    'data': None
                }, status=400)
            
            # 解析JSON数据
            try:
                data = json.loads(request.body)
                request.json = data
            except json.JSONDecodeError:
                return JsonResponse({
                    'code': 400,
                    'message': 'JSON格式错误',
                    'success': False,
                    'data': None
                }, status=400)
            
            # 检查必需字段
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data:
                        missing_fields.append(field)
                
                if missing_fields:
                    return JsonResponse({
                        'code': 400,
                        'message': f'缺少必需字段: {", ".join(missing_fields)}',
                        'success': False,
                        'data': None
                    }, status=400)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
