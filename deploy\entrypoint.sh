#!/bin/bash

# 等待MySQL启动
echo "等待MySQL启动..."
while ! nc -z mysql 3306; do
  sleep 1
done
echo "MySQL已启动"

# 等待Redis启动
echo "等待Redis启动..."
while ! nc -z redis 6379; do
  sleep 1
done
echo "Redis已启动"

# 运行数据库迁移
echo "运行数据库迁移..."
python manage.py migrate

# 收集静态文件
echo "收集静态文件..."
python manage.py collectstatic --noinput

# 创建超级用户（如果不存在）
echo "创建超级用户..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('超级用户已创建: admin/admin123456')
else:
    print('超级用户已存在')
EOF

# 启动Django应用
echo "启动Django应用..."
exec gunicorn config.wsgi:application \
    --bind 0.0.0.0:8000 \
    --workers 3 \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --access-logfile - \
    --error-logfile -
