"""
岗位管理视图
"""
import logging
from django.utils import timezone
from django.db.models import Q, Count, Avg
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from utils.response import APIResponse
from utils.permissions import IsAdminUser, IsCandidateUser, IsRecruiterUser
from utils.pagination import CustomPageNumberPagination
from .models import PositionCategory, Position, PositionApplication, PositionFavorite, PositionView
from .serializers import (
    PositionCategorySerializer, PositionSerializer, PositionCreateSerializer,
    PositionListSerializer, PositionUpdateSerializer, PositionApplicationSerializer,
    PositionApplicationCreateSerializer, PositionFavoriteSerializer,
    PositionSearchSerializer
)

logger = logging.getLogger(__name__)


class PositionCategoryViewSet(ModelViewSet):
    """岗位分类管理"""
    
    queryset = PositionCategory.objects.filter(is_active=True)
    serializer_class = PositionCategorySerializer
    pagination_class = None  # 分类数据通常不需要分页
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        else:
            permission_classes = [IsAdminUser]
        
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        """获取分类树形结构"""
        return super().get_queryset().filter(parent=None).order_by('sort_order')
    
    @action(detail=False, methods=['get'])
    def tree(self, request):
        """获取分类树"""
        categories = self.get_queryset()
        serializer = self.get_serializer(categories, many=True)
        return APIResponse.success(serializer.data)


class PositionViewSet(ModelViewSet):
    """岗位管理"""
    
    queryset = Position.objects.all()
    serializer_class = PositionSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'location', 'education_required', 'work_type', 'status']
    search_fields = ['title', 'description', 'department', 'organization']
    ordering_fields = ['created_at', 'published_at', 'application_count', 'view_count']
    ordering = ['-is_featured', '-is_urgent', '-published_at']
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve', 'search']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsRecruiterUser]
        elif self.action in ['apply', 'favorite', 'unfavorite']:
            permission_classes = [IsCandidateUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return PositionCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return PositionUpdateSerializer
        elif self.action == 'list':
            return PositionListSerializer
        return PositionSerializer
    
    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()
        
        # 非管理员和非创建者只能看到已发布的岗位
        if not (self.request.user.is_authenticated and 
                (self.request.user.is_staff or self.request.user.user_type == 'recruiter')):
            queryset = queryset.filter(status='active')
        
        # 招聘单位只能看到自己创建的岗位
        elif (self.request.user.is_authenticated and 
              self.request.user.user_type == 'recruiter' and 
              not self.request.user.is_staff):
            queryset = queryset.filter(created_by=self.request.user)
        
        return queryset.select_related('category', 'created_by')
    
    def retrieve(self, request, *args, **kwargs):
        """获取岗位详情并记录浏览"""
        instance = self.get_object()
        
        # 记录浏览行为
        if request.user.is_authenticated:
            PositionView.objects.get_or_create(
                position=instance,
                user=request.user,
                defaults={
                    'ip_address': self.get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                }
            )
        
        # 更新浏览次数
        instance.view_count += 1
        instance.save(update_fields=['view_count'])
        
        serializer = self.get_serializer(instance)
        return APIResponse.success(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """创建岗位"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            position = serializer.save()
            return APIResponse.success(
                PositionSerializer(position).data,
                message="岗位创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)
    
    def update(self, request, *args, **kwargs):
        """更新岗位"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # 检查权限：只有创建者或管理员可以修改
        if not (request.user.is_staff or instance.created_by == request.user):
            return APIResponse.forbidden("无权修改此岗位")
        
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                PositionSerializer(instance).data,
                message="岗位信息更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)
    
    @action(detail=False, methods=['post'])
    def search(self, request):
        """高级搜索"""
        serializer = PositionSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.error("搜索参数错误", data=serializer.errors)
        
        data = serializer.validated_data
        queryset = self.get_queryset().filter(status='active')
        
        # 关键词搜索
        keyword = data.get('keyword')
        if keyword:
            queryset = queryset.filter(
                Q(title__icontains=keyword) |
                Q(description__icontains=keyword) |
                Q(department__icontains=keyword) |
                Q(organization__icontains=keyword)
            )
        
        # 分类筛选
        category_id = data.get('category_id')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # 地点筛选
        location = data.get('location')
        if location:
            queryset = queryset.filter(
                Q(location__icontains=location) |
                Q(city__icontains=location) |
                Q(province__icontains=location)
            )
        
        # 学历筛选
        education_required = data.get('education_required')
        if education_required:
            queryset = queryset.filter(education_required=education_required)
        
        # 薪资筛选
        salary_min = data.get('salary_min')
        salary_max = data.get('salary_max')
        if salary_min:
            queryset = queryset.filter(
                Q(salary_min__gte=salary_min) | Q(salary_min__isnull=True)
            )
        if salary_max:
            queryset = queryset.filter(
                Q(salary_max__lte=salary_max) | Q(salary_max__isnull=True)
            )
        
        # 工作类型筛选
        work_type = data.get('work_type')
        if work_type:
            queryset = queryset.filter(work_type=work_type)
        
        # 推荐和紧急筛选
        is_featured = data.get('is_featured')
        if is_featured is not None:
            queryset = queryset.filter(is_featured=is_featured)
        
        is_urgent = data.get('is_urgent')
        if is_urgent is not None:
            queryset = queryset.filter(is_urgent=is_urgent)
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PositionListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = PositionListSerializer(queryset, many=True)
        return APIResponse.success(serializer.data)
    
    @action(detail=True, methods=['post'])
    def apply(self, request, pk=None):
        """申请岗位"""
        position = self.get_object()
        
        # 检查岗位状态
        if position.status != 'active':
            return APIResponse.error("该岗位未开放申请")
        
        if position.is_deadline_passed:
            return APIResponse.error("申请截止时间已过")
        
        # 检查是否已申请
        if PositionApplication.objects.filter(position=position, candidate=request.user).exists():
            return APIResponse.error("您已申请过此岗位")
        
        # 创建申请记录
        application = PositionApplication.objects.create(
            position=position,
            candidate=request.user,
            cover_letter=request.data.get('cover_letter', ''),
            resume=request.FILES.get('resume')
        )
        
        # 更新申请人数
        position.application_count = position.applications.count()
        position.save(update_fields=['application_count'])
        
        return APIResponse.success(
            PositionApplicationSerializer(application).data,
            message="申请成功"
        )
    
    @action(detail=True, methods=['post'])
    def favorite(self, request, pk=None):
        """收藏岗位"""
        position = self.get_object()
        
        favorite, created = PositionFavorite.objects.get_or_create(
            position=position,
            candidate=request.user
        )
        
        if created:
            # 更新收藏次数
            position.favorite_count = position.favorites.count()
            position.save(update_fields=['favorite_count'])
            return APIResponse.success(message="收藏成功")
        else:
            return APIResponse.error("已收藏过此岗位")
    
    @action(detail=True, methods=['post'])
    def unfavorite(self, request, pk=None):
        """取消收藏"""
        position = self.get_object()
        
        try:
            favorite = PositionFavorite.objects.get(position=position, candidate=request.user)
            favorite.delete()
            
            # 更新收藏次数
            position.favorite_count = position.favorites.count()
            position.save(update_fields=['favorite_count'])
            
            return APIResponse.success(message="取消收藏成功")
        except PositionFavorite.DoesNotExist:
            return APIResponse.error("未收藏此岗位")
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取岗位统计信息"""
        position = self.get_object()
        
        # 申请统计
        applications = position.applications.all()
        application_stats = {
            'total': applications.count(),
            'pending': applications.filter(status='pending').count(),
            'reviewing': applications.filter(status='reviewing').count(),
            'interview': applications.filter(status='interview').count(),
            'approved': applications.filter(status='approved').count(),
            'rejected': applications.filter(status='rejected').count(),
        }
        
        # 浏览统计
        views = position.views.all()
        view_stats = {
            'total_views': views.count(),
            'unique_views': views.values('user').distinct().count(),
            'today_views': views.filter(viewed_at__date=timezone.now().date()).count(),
        }
        
        return APIResponse.success({
            'application_stats': application_stats,
            'view_stats': view_stats,
            'favorite_count': position.favorite_count,
            'competition_ratio': round(position.application_count / position.recruitment_count, 2) if position.recruitment_count > 0 else 0,
        })
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PositionApplicationViewSet(ModelViewSet):
    """岗位申请管理"""

    queryset = PositionApplication.objects.all()
    serializer_class = PositionApplicationSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['position', 'status']
    search_fields = ['candidate__username', 'candidate__real_name']
    ordering_fields = ['applied_at']
    ordering = ['-applied_at']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action == 'create':
            permission_classes = [IsCandidateUser]
        elif self.action in ['approve', 'reject', 'interview']:
            permission_classes = [IsRecruiterUser]
        else:
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return PositionApplicationCreateSerializer
        return PositionApplicationSerializer

    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()

        # 考生只能看到自己的申请记录
        if (self.request.user.is_authenticated and
            self.request.user.user_type == 'candidate'):
            queryset = queryset.filter(candidate=self.request.user)

        # 招聘单位只能看到自己岗位的申请记录
        elif (self.request.user.is_authenticated and
              self.request.user.user_type == 'recruiter' and
              not self.request.user.is_staff):
            queryset = queryset.filter(position__created_by=self.request.user)

        return queryset.select_related('position', 'candidate', 'reviewed_by')

    def create(self, request, *args, **kwargs):
        """创建申请记录"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            application = serializer.save()
            return APIResponse.success(
                PositionApplicationSerializer(application).data,
                message="申请成功"
            )
        return APIResponse.error("申请失败", data=serializer.errors)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审核通过"""
        application = self.get_object()

        # 检查权限：只有岗位创建者可以审核
        if application.position.created_by != request.user and not request.user.is_staff:
            return APIResponse.forbidden("无权审核此申请")

        application.status = 'approved'
        application.reviewed_by = request.user
        application.reviewed_at = timezone.now()
        application.review_notes = request.data.get('review_notes', '')
        application.save()

        return APIResponse.success(message="审核通过")

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """审核拒绝"""
        application = self.get_object()

        # 检查权限：只有岗位创建者可以审核
        if application.position.created_by != request.user and not request.user.is_staff:
            return APIResponse.forbidden("无权审核此申请")

        application.status = 'rejected'
        application.reviewed_by = request.user
        application.reviewed_at = timezone.now()
        application.review_notes = request.data.get('review_notes', '')
        application.save()

        return APIResponse.success(message="审核拒绝")

    @action(detail=True, methods=['post'])
    def interview(self, request, pk=None):
        """安排面试"""
        application = self.get_object()

        # 检查权限：只有岗位创建者可以安排面试
        if application.position.created_by != request.user and not request.user.is_staff:
            return APIResponse.forbidden("无权安排面试")

        application.status = 'interview'
        application.interview_time = request.data.get('interview_time')
        application.interview_location = request.data.get('interview_location', '')
        application.interview_notes = request.data.get('interview_notes', '')
        application.save()

        return APIResponse.success(message="面试安排成功")

    @action(detail=False, methods=['get'])
    def my_applications(self, request):
        """我的申请记录"""
        if request.user.user_type != 'candidate':
            return APIResponse.error("只有考生可以查看申请记录")

        applications = self.get_queryset().filter(candidate=request.user)
        page = self.paginate_queryset(applications)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(applications, many=True)
        return APIResponse.success(serializer.data)


class PositionFavoriteViewSet(ModelViewSet):
    """岗位收藏管理"""

    queryset = PositionFavorite.objects.all()
    serializer_class = PositionFavoriteSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsCandidateUser]

    def get_queryset(self):
        """只能查看自己的收藏"""
        return super().get_queryset().filter(candidate=self.request.user).select_related('position')

    def create(self, request, *args, **kwargs):
        """收藏岗位"""
        position_id = request.data.get('position_id')

        if not position_id:
            return APIResponse.error("请指定岗位ID")

        try:
            position = Position.objects.get(id=position_id, status='active')
        except Position.DoesNotExist:
            return APIResponse.error("岗位不存在或未开放")

        favorite, created = PositionFavorite.objects.get_or_create(
            position=position,
            candidate=request.user
        )

        if created:
            # 更新收藏次数
            position.favorite_count = position.favorites.count()
            position.save(update_fields=['favorite_count'])

            return APIResponse.success(
                self.get_serializer(favorite).data,
                message="收藏成功"
            )
        else:
            return APIResponse.error("已收藏过此岗位")

    def destroy(self, request, *args, **kwargs):
        """取消收藏"""
        instance = self.get_object()
        position = instance.position

        instance.delete()

        # 更新收藏次数
        position.favorite_count = position.favorites.count()
        position.save(update_fields=['favorite_count'])

        return APIResponse.success(message="取消收藏成功")

    @action(detail=False, methods=['get'])
    def my_favorites(self, request):
        """我的收藏"""
        favorites = self.get_queryset()
        page = self.paginate_queryset(favorites)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(favorites, many=True)
        return APIResponse.success(serializer.data)
