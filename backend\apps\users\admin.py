"""
用户管理后台配置
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, CandidateProfile, RecruiterProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""
    
    list_display = ['username', 'nickname', 'real_name', 'user_type', 'phone', 'is_active', 'created_at']
    list_filter = ['user_type', 'is_active', 'is_staff', 'created_at']
    search_fields = ['username', 'nickname', 'real_name', 'phone', 'email']
    ordering = ['-created_at']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('扩展信息', {
            'fields': ('phone', 'nickname', 'avatar', 'user_type', 'wechat_openid', 'wechat_unionid')
        }),
        ('个人信息', {
            'fields': ('real_name', 'id_card', 'gender', 'birth_date')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


class CandidateProfileInline(admin.StackedInline):
    """考生信息内联"""
    model = CandidateProfile
    extra = 0
    readonly_fields = ['created_at', 'updated_at', 'verified_at']


@admin.register(CandidateProfile)
class CandidateProfileAdmin(admin.ModelAdmin):
    """考生信息管理"""
    
    list_display = ['user', 'real_name', 'education', 'major', 'work_years', 'is_verified', 'created_at']
    list_filter = ['education', 'political_status', 'is_verified', 'created_at']
    search_fields = ['user__username', 'user__real_name', 'major', 'graduation_school']
    ordering = ['-created_at']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['user']
        }),
        ('教育背景', {
            'fields': ['education', 'major', 'graduation_school', 'graduation_year']
        }),
        ('工作经历', {
            'fields': ['work_experience', 'current_position', 'work_years']
        }),
        ('联系信息', {
            'fields': ['address', 'emergency_contact', 'emergency_phone']
        }),
        ('其他信息', {
            'fields': ['political_status', 'skills', 'certificates']
        }),
        ('求职偏好', {
            'fields': ['preferred_location', 'preferred_salary_min', 'preferred_salary_max']
        }),
        ('审核信息', {
            'fields': ['is_verified', 'verified_at', 'verified_by'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at', 'verified_at']
    
    def real_name(self, obj):
        return obj.user.real_name
    real_name.short_description = '真实姓名'
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.verified_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(RecruiterProfile)
class RecruiterProfileAdmin(admin.ModelAdmin):
    """招聘单位信息管理"""
    
    list_display = ['organization_name', 'organization_type', 'contact_person', 'contact_phone', 'is_verified', 'created_at']
    list_filter = ['organization_type', 'is_verified', 'created_at']
    search_fields = ['organization_name', 'contact_person', 'contact_phone']
    ordering = ['-created_at']
    
    fieldsets = [
        ('基本信息', {
            'fields': ['user']
        }),
        ('单位信息', {
            'fields': ['organization_name', 'organization_type', 'organization_code', 'legal_representative']
        }),
        ('联系信息', {
            'fields': ['contact_person', 'contact_phone', 'contact_email', 'office_address']
        }),
        ('单位描述', {
            'fields': ['description', 'website']
        }),
        ('审核信息', {
            'fields': ['is_verified', 'verified_at', 'verified_by'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at', 'verified_at']
    
    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.verified_by = request.user
        super().save_model(request, obj, form, change)
