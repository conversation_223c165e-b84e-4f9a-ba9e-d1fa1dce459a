<!--index.wxml-->
<view class="container">
  <!-- 顶部轮播图 -->
  <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" mode="aspectFill" class="banner-image" bindtap="onBannerTap" data-item="{{item}}"/>
    </swiper-item>
  </swiper>

  <!-- 快捷入口 -->
  <view class="quick-entry">
    <view class="entry-item" bindtap="navigateToExams">
      <van-icon name="edit" size="24px" color="#1976D2"/>
      <text>考试报名</text>
    </view>
    <view class="entry-item" bindtap="navigateToPositions">
      <van-icon name="manager" size="24px" color="#1976D2"/>
      <text>岗位查看</text>
    </view>
    <view class="entry-item" bindtap="navigateToScores">
      <van-icon name="chart-trending-o" size="24px" color="#1976D2"/>
      <text>成绩查询</text>
    </view>
    <view class="entry-item" bindtap="navigateToRecommendations">
      <van-icon name="star-o" size="24px" color="#1976D2"/>
      <text>智能推荐</text>
    </view>
  </view>

  <!-- 最新考试 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">最新考试</text>
      <text class="section-more" bindtap="navigateToExams">更多 ></text>
    </view>
    <view class="exam-list">
      <view wx:for="{{latestExams}}" wx:key="id" class="exam-item" bindtap="onExamTap" data-id="{{item.id}}">
        <view class="exam-info">
          <text class="exam-title">{{item.name}}</text>
          <text class="exam-time">报名时间：{{item.registration_start}} - {{item.registration_end}}</text>
          <text class="exam-location">考试地点：{{item.exam_location}}</text>
        </view>
        <view class="exam-status">
          <van-tag wx:if="{{item.is_registration_open}}" type="success">报名中</van-tag>
          <van-tag wx:elif="{{item.is_ongoing}}" type="warning">进行中</van-tag>
          <van-tag wx:else type="default">已结束</van-tag>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门岗位 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门岗位</text>
      <text class="section-more" bindtap="navigateToPositions">更多 ></text>
    </view>
    <view class="position-list">
      <view wx:for="{{hotPositions}}" wx:key="id" class="position-item" bindtap="onPositionTap" data-id="{{item.id}}">
        <view class="position-info">
          <text class="position-title">{{item.title}}</text>
          <text class="position-department">{{item.department}}</text>
          <text class="position-location">{{item.location}}</text>
        </view>
        <view class="position-meta">
          <text class="position-salary">{{item.salary_range}}</text>
          <text class="position-count">招聘{{item.recruitment_count}}人</text>
          <van-tag wx:if="{{item.is_featured}}" type="danger" size="mini">推荐</van-tag>
          <van-tag wx:if="{{item.is_urgent}}" type="warning" size="mini">紧急</van-tag>
        </view>
      </view>
    </view>
  </view>

  <!-- 个性化推荐 -->
  <view class="section" wx:if="{{isLogin && recommendations.length > 0}}">
    <view class="section-header">
      <text class="section-title">为您推荐</text>
      <text class="section-more" bindtap="navigateToRecommendations">更多 ></text>
    </view>
    <view class="recommendation-list">
      <view wx:for="{{recommendations}}" wx:key="id" class="recommendation-item" bindtap="onRecommendationTap" data-item="{{item}}">
        <view class="recommendation-info">
          <text class="recommendation-title">{{item.position.title}}</text>
          <text class="recommendation-reason">{{item.reason}}</text>
          <text class="recommendation-score">匹配度：{{item.score}}%</text>
        </view>
        <view class="recommendation-meta">
          <text class="recommendation-department">{{item.position.department}}</text>
          <text class="recommendation-location">{{item.position.location}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 系统公告 -->
  <view class="section" wx:if="{{notices.length > 0}}">
    <view class="section-header">
      <text class="section-title">系统公告</text>
    </view>
    <view class="notice-list">
      <view wx:for="{{notices}}" wx:key="id" class="notice-item" bindtap="onNoticeTap" data-id="{{item.id}}">
        <van-icon name="volume-o" size="16px" color="#ff6b35"/>
        <text class="notice-title">{{item.title}}</text>
        <text class="notice-time">{{item.created_at}}</text>
      </view>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- 加载组件 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#1976D2" vertical>加载中...</van-loading>
