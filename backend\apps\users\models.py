"""
用户管理模型
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone


class User(AbstractUser):
    """自定义用户模型"""
    
    USER_TYPE_CHOICES = [
        ('candidate', '考生'),
        ('recruiter', '招聘单位'),
        ('admin', '管理员'),
    ]
    
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
        ('other', '其他'),
    ]
    
    # 基础信息
    phone = models.CharField('手机号', max_length=11, blank=True, null=True)
    nickname = models.CharField('昵称', max_length=50, blank=True, null=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    user_type = models.CharField('用户类型', max_length=20, choices=USER_TYPE_CHOICES, default='candidate')
    

    # 个人信息
    real_name = models.Char<PERSON>ield('真实姓名', max_length=50, blank=True, null=True)
    id_card = models.CharField('身份证号', max_length=18, blank=True, null=True)
    gender = models.CharField('性别', max_length=10, choices=GENDER_CHOICES, blank=True, null=True)
    birth_date = models.DateField('出生日期', blank=True, null=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'users_user'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.nickname or self.username
    
    @property
    def display_name(self):
        """显示名称"""
        return self.real_name or self.nickname or self.username


class CandidateProfile(models.Model):
    """考生详细信息"""
    
    EDUCATION_CHOICES = [
        ('high_school', '高中'),
        ('junior_college', '大专'),
        ('bachelor', '本科'),
        ('master', '硕士'),
        ('doctor', '博士'),
    ]
    
    POLITICAL_STATUS_CHOICES = [
        ('masses', '群众'),
        ('league_member', '共青团员'),
        ('party_member', '中共党员'),
        ('democratic_party', '民主党派'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户', related_name='candidate_profile')
    
    # 教育背景
    education = models.CharField('学历', max_length=20, choices=EDUCATION_CHOICES, blank=True, null=True)
    major = models.CharField('专业', max_length=100, blank=True, null=True)
    graduation_school = models.CharField('毕业院校', max_length=200, blank=True, null=True)
    graduation_year = models.IntegerField('毕业年份', blank=True, null=True)
    
    # 工作经历
    work_experience = models.TextField('工作经历', blank=True, null=True)
    current_position = models.CharField('当前职位', max_length=100, blank=True, null=True)
    work_years = models.IntegerField('工作年限', default=0)
    
    # 联系信息
    address = models.CharField('地址', max_length=200, blank=True, null=True)
    emergency_contact = models.CharField('紧急联系人', max_length=50, blank=True, null=True)
    emergency_phone = models.CharField('紧急联系电话', max_length=11, blank=True, null=True)
    
    # 政治面貌
    political_status = models.CharField('政治面貌', max_length=20, choices=POLITICAL_STATUS_CHOICES, default='masses')
    
    # 特长技能
    skills = models.TextField('特长技能', blank=True, null=True)
    certificates = models.TextField('证书资格', blank=True, null=True)
    
    # 求职偏好
    preferred_location = models.CharField('期望工作地点', max_length=100, blank=True, null=True)
    preferred_salary_min = models.DecimalField('期望薪资下限', max_digits=10, decimal_places=2, blank=True, null=True)
    preferred_salary_max = models.DecimalField('期望薪资上限', max_digits=10, decimal_places=2, blank=True, null=True)
    
    # 审核状态
    is_verified = models.BooleanField('是否已审核', default=False)
    verified_at = models.DateTimeField('审核时间', blank=True, null=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, 
                                   related_name='verified_candidates', verbose_name='审核人')
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'users_candidate_profile'
        verbose_name = '考生信息'
        verbose_name_plural = '考生信息'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.display_name}的考生信息"
    
    @property
    def age(self):
        """计算年龄"""
        if self.user.birth_date:
            today = timezone.now().date()
            return today.year - self.user.birth_date.year - (
                (today.month, today.day) < (self.user.birth_date.month, self.user.birth_date.day)
            )
        return None


class RecruiterProfile(models.Model):
    """招聘单位信息"""
    
    ORGANIZATION_TYPE_CHOICES = [
        ('government', '政府机关'),
        ('institution', '事业单位'),
        ('enterprise', '国有企业'),
        ('other', '其他'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户', related_name='recruiter_profile')
    
    # 单位信息
    organization_name = models.CharField('单位名称', max_length=200)
    organization_type = models.CharField('单位类型', max_length=20, choices=ORGANIZATION_TYPE_CHOICES)
    organization_code = models.CharField('统一社会信用代码', max_length=50, blank=True, null=True)
    legal_representative = models.CharField('法定代表人', max_length=50, blank=True, null=True)
    
    # 联系信息
    contact_person = models.CharField('联系人', max_length=50)
    contact_phone = models.CharField('联系电话', max_length=20)
    contact_email = models.EmailField('联系邮箱', blank=True, null=True)
    office_address = models.CharField('办公地址', max_length=200, blank=True, null=True)
    
    # 单位描述
    description = models.TextField('单位简介', blank=True, null=True)
    website = models.URLField('官方网站', blank=True, null=True)
    
    # 审核状态
    is_verified = models.BooleanField('是否已审核', default=False)
    verified_at = models.DateTimeField('审核时间', blank=True, null=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True,
                                   related_name='verified_recruiters', verbose_name='审核人')
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'users_recruiter_profile'
        verbose_name = '招聘单位信息'
        verbose_name_plural = '招聘单位信息'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.organization_name
