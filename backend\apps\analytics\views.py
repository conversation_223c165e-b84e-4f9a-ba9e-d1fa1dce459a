"""
数据分析视图
"""
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum, Max, Min
from django.http import HttpResponse
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter

from utils.response import APIResponse
from utils.permissions import IsAdminUser
from utils.pagination import CustomPageNumberPagination
from apps.users.models import User
from apps.exams.models import Exam, ExamRegistration, Score
from apps.positions.models import Position, PositionApplication
from apps.recommendations.models import RecommendationRecord, UserBehavior
from .models import (
    SystemStatistics, ExamAnalytics, PositionAnalytics, 
    UserAnalytics, RecommendationAnalytics
)
from .serializers import (
    SystemStatisticsSerializer, ExamAnalyticsSerializer, PositionAnalyticsSerializer,
    UserAnalyticsSerializer, RecommendationAnalyticsSerializer, DashboardDataSerializer,
    AnalyticsRequestSerializer, ExportRequestSerializer
)

logger = logging.getLogger(__name__)


class SystemStatisticsViewSet(ModelViewSet):
    """系统统计管理"""
    
    queryset = SystemStatistics.objects.all()
    serializer_class = SystemStatisticsSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['stat_type', 'stat_date']
    ordering_fields = ['stat_date']
    ordering = ['-stat_date']


class ExamAnalyticsViewSet(ModelViewSet):
    """考试分析管理"""
    
    queryset = ExamAnalytics.objects.all()
    serializer_class = ExamAnalyticsSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['exam']
    ordering_fields = ['calculated_at']
    ordering = ['-calculated_at']
    
    def get_queryset(self):
        return super().get_queryset().select_related('exam')


class PositionAnalyticsViewSet(ModelViewSet):
    """岗位分析管理"""
    
    queryset = PositionAnalytics.objects.all()
    serializer_class = PositionAnalyticsSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['position']
    ordering_fields = ['calculated_at', 'total_views', 'total_applications']
    ordering = ['-calculated_at']
    
    def get_queryset(self):
        return super().get_queryset().select_related('position')


class UserAnalyticsViewSet(ModelViewSet):
    """用户分析管理"""
    
    queryset = UserAnalytics.objects.all()
    serializer_class = UserAnalyticsSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['calculated_at', 'login_count', 'active_days']
    ordering = ['-calculated_at']
    
    def get_queryset(self):
        return super().get_queryset().select_related('user')


class RecommendationAnalyticsViewSet(ModelViewSet):
    """推荐分析管理"""
    
    queryset = RecommendationAnalytics.objects.all()
    serializer_class = RecommendationAnalyticsSerializer
    pagination_class = CustomPageNumberPagination
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['algorithm_type', 'date']
    ordering_fields = ['date', 'click_through_rate', 'conversion_rate']
    ordering = ['-date']


class DashboardView(APIView):
    """仪表板数据"""
    
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        """获取仪表板数据"""
        try:
            # 获取时间范围
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)
            
            # 概览数据
            overview = self._get_overview_data()
            
            # 趋势数据
            user_trend = self._get_user_trend(start_date, end_date)
            exam_trend = self._get_exam_trend(start_date, end_date)
            position_trend = self._get_position_trend(start_date, end_date)
            application_trend = self._get_application_trend(start_date, end_date)
            
            # 分布数据
            user_type_distribution = self._get_user_type_distribution()
            education_distribution = self._get_education_distribution()
            location_distribution = self._get_location_distribution()
            
            # 推荐效果
            recommendation_performance = self._get_recommendation_performance()
            
            dashboard_data = {
                'overview': overview,
                'user_trend': user_trend,
                'exam_trend': exam_trend,
                'position_trend': position_trend,
                'application_trend': application_trend,
                'user_type_distribution': user_type_distribution,
                'education_distribution': education_distribution,
                'location_distribution': location_distribution,
                'recommendation_performance': recommendation_performance,
            }
            
            return APIResponse.success(dashboard_data)
            
        except Exception as e:
            logger.error(f"获取仪表板数据失败: {e}")
            return APIResponse.error("获取仪表板数据失败")
    
    def _get_overview_data(self):
        """获取概览数据"""
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        
        # 用户统计
        total_users = User.objects.count()
        new_users_today = User.objects.filter(date_joined__date=today).count()
        new_users_yesterday = User.objects.filter(date_joined__date=yesterday).count()
        
        # 考试统计
        total_exams = Exam.objects.count()
        ongoing_exams = Exam.objects.filter(status='ongoing').count()
        
        # 岗位统计
        total_positions = Position.objects.count()
        active_positions = Position.objects.filter(status='active').count()
        
        # 申请统计
        total_applications = PositionApplication.objects.count()
        new_applications_today = PositionApplication.objects.filter(applied_at__date=today).count()
        
        # 推荐统计
        total_recommendations = RecommendationRecord.objects.count()
        recommendation_clicks = RecommendationRecord.objects.filter(status='clicked').count()
        
        return {
            'total_users': total_users,
            'new_users_today': new_users_today,
            'user_growth_rate': self._calculate_growth_rate(new_users_today, new_users_yesterday),
            'total_exams': total_exams,
            'ongoing_exams': ongoing_exams,
            'total_positions': total_positions,
            'active_positions': active_positions,
            'total_applications': total_applications,
            'new_applications_today': new_applications_today,
            'total_recommendations': total_recommendations,
            'recommendation_click_rate': round(recommendation_clicks / total_recommendations * 100, 2) if total_recommendations > 0 else 0,
        }
    
    def _calculate_growth_rate(self, current, previous):
        """计算增长率"""
        if previous == 0:
            return 100 if current > 0 else 0
        return round((current - previous) / previous * 100, 2)
    
    def _get_user_trend(self, start_date, end_date):
        """获取用户趋势"""
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            new_users = User.objects.filter(date_joined__date=current_date).count()
            total_users = User.objects.filter(date_joined__date__lte=current_date).count()
            
            trend_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'new_users': new_users,
                'total_users': total_users,
            })
            
            current_date += timedelta(days=1)
        
        return trend_data
    
    def _get_exam_trend(self, start_date, end_date):
        """获取考试趋势"""
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            new_exams = Exam.objects.filter(created_at__date=current_date).count()
            new_registrations = ExamRegistration.objects.filter(created_at__date=current_date).count()
            
            trend_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'new_exams': new_exams,
                'new_registrations': new_registrations,
            })
            
            current_date += timedelta(days=1)
        
        return trend_data
    
    def _get_position_trend(self, start_date, end_date):
        """获取岗位趋势"""
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            new_positions = Position.objects.filter(created_at__date=current_date).count()
            
            trend_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'new_positions': new_positions,
            })
            
            current_date += timedelta(days=1)
        
        return trend_data
    
    def _get_application_trend(self, start_date, end_date):
        """获取申请趋势"""
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            new_applications = PositionApplication.objects.filter(applied_at__date=current_date).count()
            
            trend_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'new_applications': new_applications,
            })
            
            current_date += timedelta(days=1)
        
        return trend_data
    
    def _get_user_type_distribution(self):
        """获取用户类型分布"""
        distribution = User.objects.values('user_type').annotate(count=Count('id'))
        
        type_names = {
            'candidate': '考生',
            'recruiter': '招聘单位',
            'admin': '管理员',
        }
        
        return [
            {
                'type': type_names.get(item['user_type'], item['user_type']),
                'count': item['count']
            }
            for item in distribution
        ]
    
    def _get_education_distribution(self):
        """获取学历分布"""
        from apps.users.models import CandidateProfile
        
        distribution = CandidateProfile.objects.exclude(
            education__isnull=True
        ).values('education').annotate(count=Count('id'))
        
        education_names = {
            'high_school': '高中',
            'junior_college': '大专',
            'bachelor': '本科',
            'master': '硕士',
            'doctor': '博士',
        }
        
        return [
            {
                'education': education_names.get(item['education'], item['education']),
                'count': item['count']
            }
            for item in distribution
        ]
    
    def _get_location_distribution(self):
        """获取地区分布"""
        distribution = Position.objects.exclude(
            location__isnull=True
        ).values('location').annotate(count=Count('id')).order_by('-count')[:10]
        
        return [
            {
                'location': item['location'],
                'count': item['count']
            }
            for item in distribution
        ]
    
    def _get_recommendation_performance(self):
        """获取推荐效果"""
        algorithms = ['collaborative_filtering', 'content_based', 'hybrid', 'popularity']
        performance_data = []
        
        for algorithm in algorithms:
            total_recs = RecommendationRecord.objects.filter(algorithm_type=algorithm).count()
            clicked_recs = RecommendationRecord.objects.filter(
                algorithm_type=algorithm, status='clicked'
            ).count()
            applied_recs = RecommendationRecord.objects.filter(
                algorithm_type=algorithm, status='applied'
            ).count()
            
            click_rate = round(clicked_recs / total_recs * 100, 2) if total_recs > 0 else 0
            application_rate = round(applied_recs / total_recs * 100, 2) if total_recs > 0 else 0
            
            algorithm_names = {
                'collaborative_filtering': '协同过滤',
                'content_based': '基于内容',
                'hybrid': '混合推荐',
                'popularity': '热门推荐',
            }
            
            performance_data.append({
                'algorithm': algorithm_names.get(algorithm, algorithm),
                'total_recommendations': total_recs,
                'click_rate': click_rate,
                'application_rate': application_rate,
            })
        
        return performance_data


class AnalyticsView(APIView):
    """数据分析"""

    permission_classes = [IsAdminUser]

    def post(self, request):
        """执行数据分析"""
        serializer = AnalyticsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.error("参数错误", data=serializer.errors)

        data = serializer.validated_data
        analysis_type = data['analysis_type']

        try:
            if analysis_type == 'exam_report':
                result = self._generate_exam_report(data)
            elif analysis_type == 'position_report':
                result = self._generate_position_report(data)
            elif analysis_type == 'user_behavior':
                result = self._analyze_user_behavior(data)
            elif analysis_type == 'trend_analysis':
                result = self._analyze_trends(data)
            elif analysis_type == 'comparison':
                result = self._compare_items(data)
            elif analysis_type == 'prediction':
                result = self._predict_trends(data)
            else:
                return APIResponse.error("不支持的分析类型")

            return APIResponse.success(result)

        except Exception as e:
            logger.error(f"数据分析失败: {e}")
            return APIResponse.error("数据分析失败")

    def _generate_exam_report(self, data):
        """生成考试报告"""
        exam_id = data.get('filters', {}).get('exam_id')
        if not exam_id:
            raise ValueError("缺少考试ID")

        try:
            exam = Exam.objects.get(id=exam_id)
        except Exam.DoesNotExist:
            raise ValueError("考试不存在")

        # 基础统计
        registrations = exam.registrations.all()
        scores = exam.scores.all()

        basic_stats = {
            'registration_count': registrations.count(),
            'approved_count': registrations.filter(status='approved').count(),
            'participant_count': scores.count(),
            'qualified_count': scores.filter(is_qualified=True).count(),
        }

        # 成绩分析
        if scores.exists():
            score_stats = scores.aggregate(
                avg_initial=Avg('initial_score'),
                avg_assessment=Avg('assessment_score'),
                avg_total=Avg('total_score'),
                max_total=Max('total_score'),
                min_total=Min('total_score')
            )

            score_analysis = {
                'average_scores': {
                    'initial': round(score_stats['avg_initial'] or 0, 2),
                    'assessment': round(score_stats['avg_assessment'] or 0, 2),
                    'total': round(score_stats['avg_total'] or 0, 2),
                },
                'score_range': {
                    'max': score_stats['max_total'],
                    'min': score_stats['min_total'],
                },
                'qualification_rate': round(basic_stats['qualified_count'] / basic_stats['participant_count'] * 100, 2) if basic_stats['participant_count'] > 0 else 0,
            }
        else:
            score_analysis = {}

        # 分布分析
        distributions = self._analyze_exam_distributions(exam)

        # 排名信息
        rankings = list(scores.filter(is_qualified=True).order_by('rank')[:10].values(
            'candidate__real_name', 'candidate__username', 'total_score', 'rank'
        ))

        return {
            'exam_id': exam.id,
            'exam_name': exam.name,
            'basic_stats': basic_stats,
            'score_analysis': score_analysis,
            'distributions': distributions,
            'rankings': rankings,
        }

    def _analyze_exam_distributions(self, exam):
        """分析考试分布"""
        scores = exam.scores.all()

        # 分数段分布
        score_ranges = [
            (90, 100, '优秀'),
            (80, 89, '良好'),
            (70, 79, '中等'),
            (60, 69, '及格'),
            (0, 59, '不及格'),
        ]

        score_distribution = []
        for min_score, max_score, label in score_ranges:
            count = scores.filter(
                total_score__gte=min_score,
                total_score__lte=max_score
            ).count()
            score_distribution.append({
                'range': f"{min_score}-{max_score}",
                'label': label,
                'count': count
            })

        return {
            'score_distribution': score_distribution,
        }

    def _generate_position_report(self, data):
        """生成岗位报告"""
        position_id = data.get('filters', {}).get('position_id')
        if not position_id:
            raise ValueError("缺少岗位ID")

        try:
            position = Position.objects.get(id=position_id)
        except Position.DoesNotExist:
            raise ValueError("岗位不存在")

        # 基础统计
        applications = position.applications.all()

        basic_stats = {
            'view_count': position.view_count,
            'application_count': applications.count(),
            'favorite_count': position.favorite_count,
            'recruitment_count': position.recruitment_count,
        }

        # 申请分析
        application_analysis = {
            'status_distribution': list(applications.values('status').annotate(count=Count('id'))),
            'application_trend': self._get_position_application_trend(position),
        }

        # 竞争分析
        competition_analysis = {
            'competition_ratio': round(basic_stats['application_count'] / basic_stats['recruitment_count'], 2) if basic_stats['recruitment_count'] > 0 else 0,
            'view_to_application_rate': round(basic_stats['application_count'] / basic_stats['view_count'] * 100, 2) if basic_stats['view_count'] > 0 else 0,
        }

        # 推荐效果
        recommendations = RecommendationRecord.objects.filter(position=position)
        recommendation_performance = {
            'total_recommendations': recommendations.count(),
            'click_count': recommendations.filter(status='clicked').count(),
            'application_count': recommendations.filter(status='applied').count(),
        }

        return {
            'position_id': position.id,
            'position_title': position.title,
            'basic_stats': basic_stats,
            'application_analysis': application_analysis,
            'competition_analysis': competition_analysis,
            'recommendation_performance': recommendation_performance,
        }

    def _get_position_application_trend(self, position):
        """获取岗位申请趋势"""
        from django.db.models import TruncDate

        trend_data = position.applications.extra(
            select={'date': 'DATE(applied_at)'}
        ).values('date').annotate(count=Count('id')).order_by('date')

        return list(trend_data)


class ExportView(APIView):
    """数据导出"""

    permission_classes = [IsAdminUser]

    def post(self, request):
        """导出数据"""
        serializer = ExportRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.error("参数错误", data=serializer.errors)

        data = serializer.validated_data
        export_type = data['export_type']
        data_type = data['data_type']

        try:
            if export_type == 'excel':
                response = self._export_to_excel(data)
            elif export_type == 'csv':
                response = self._export_to_csv(data)
            elif export_type == 'pdf':
                response = self._export_to_pdf(data)
            else:
                return APIResponse.error("不支持的导出格式")

            return response

        except Exception as e:
            logger.error(f"数据导出失败: {e}")
            return APIResponse.error("数据导出失败")

    def _export_to_excel(self, data):
        """导出到Excel"""
        try:
            import pandas as pd
            from io import BytesIO

            # 获取数据
            df = self._get_export_data(data)

            # 创建Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='数据', index=False)

            output.seek(0)

            # 创建响应
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{data["data_type"]}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

            return response

        except ImportError:
            raise ValueError("Excel导出需要安装pandas和openpyxl")

    def _export_to_csv(self, data):
        """导出到CSV"""
        try:
            import pandas as pd

            # 获取数据
            df = self._get_export_data(data)

            # 创建CSV响应
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="{data["data_type"]}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

            df.to_csv(response, index=False, encoding='utf-8-sig')

            return response

        except ImportError:
            raise ValueError("CSV导出需要安装pandas")

    def _get_export_data(self, data):
        """获取导出数据"""
        import pandas as pd

        data_type = data['data_type']
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        filters = data.get('filters', {})
        fields = data.get('fields')

        if data_type == 'system_stats':
            queryset = SystemStatistics.objects.all()
            if start_date:
                queryset = queryset.filter(stat_date__gte=start_date)
            if end_date:
                queryset = queryset.filter(stat_date__lte=end_date)

            df = pd.DataFrame(list(queryset.values()))

        elif data_type == 'exam_analytics':
            queryset = ExamAnalytics.objects.select_related('exam')
            df_data = []

            for analytics in queryset:
                row = {
                    '考试名称': analytics.exam.name,
                    '报名人数': analytics.registration_count,
                    '参考人数': analytics.participant_count,
                    '出考率': analytics.attendance_rate,
                    '合格人数': analytics.qualified_count,
                    '合格率': analytics.qualification_rate,
                    '平均总成绩': analytics.avg_total_score,
                    '计算时间': analytics.calculated_at,
                }
                df_data.append(row)

            df = pd.DataFrame(df_data)

        elif data_type == 'position_analytics':
            queryset = PositionAnalytics.objects.select_related('position')
            df_data = []

            for analytics in queryset:
                row = {
                    '岗位名称': analytics.position.title,
                    '部门': analytics.position.department,
                    '浏览次数': analytics.total_views,
                    '申请人数': analytics.total_applications,
                    '收藏次数': analytics.total_favorites,
                    '竞争比例': analytics.competition_ratio,
                    '计算时间': analytics.calculated_at,
                }
                df_data.append(row)

            df = pd.DataFrame(df_data)

        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

        # 筛选字段
        if fields and not df.empty:
            available_fields = [field for field in fields if field in df.columns]
            if available_fields:
                df = df[available_fields]

        return df
