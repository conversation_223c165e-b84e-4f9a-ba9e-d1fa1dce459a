"""
推荐算法实现
"""
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
from django.db.models import Q, Count, Avg
from django.conf import settings
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler
import logging

from apps.users.models import User
from apps.positions.models import Position, PositionApplication
from .models import UserBehavior, UserPreference, RecommendationRecord, UserSimilarity

logger = logging.getLogger(__name__)


class BaseRecommendationAlgorithm:
    """推荐算法基类"""
    
    def __init__(self, user: User):
        self.user = user
        self.recommendation_settings = getattr(settings, 'RECOMMENDATION_SETTINGS', {})
    
    def recommend(self, count: int = 10, **kwargs) -> List[Dict]:
        """
        生成推荐结果
        
        Args:
            count: 推荐数量
            **kwargs: 其他参数
            
        Returns:
            推荐结果列表
        """
        raise NotImplementedError("子类必须实现recommend方法")
    
    def get_user_interactions(self, user: User) -> pd.DataFrame:
        """获取用户交互数据"""
        behaviors = UserBehavior.objects.filter(user=user).select_related('position')
        
        data = []
        for behavior in behaviors:
            data.append({
                'user_id': behavior.user_id,
                'position_id': behavior.position_id,
                'behavior_type': behavior.behavior_type,
                'score': behavior.behavior_score,
                'created_at': behavior.created_at
            })
        
        return pd.DataFrame(data)
    
    def get_position_features(self, position: Position) -> Dict:
        """提取岗位特征"""
        return {
            'category_id': position.category_id or 0,
            'education_required': self._encode_education(position.education_required),
            'work_type': self._encode_work_type(position.work_type),
            'salary_avg': self._calculate_salary_avg(position),
            'location_code': self._encode_location(position.location),
            'department_code': hash(position.department) % 1000 if position.department else 0,
        }
    
    def _encode_education(self, education: str) -> int:
        """编码学历要求"""
        education_map = {
            'high_school': 1,
            'junior_college': 2,
            'bachelor': 3,
            'master': 4,
            'doctor': 5,
        }
        return education_map.get(education, 0)
    
    def _encode_work_type(self, work_type: str) -> int:
        """编码工作类型"""
        work_type_map = {
            'full_time': 1,
            'part_time': 2,
            'contract': 3,
            'intern': 4,
        }
        return work_type_map.get(work_type, 0)
    
    def _calculate_salary_avg(self, position: Position) -> float:
        """计算平均薪资"""
        if position.salary_min and position.salary_max:
            return float(position.salary_min + position.salary_max) / 2
        elif position.salary_min:
            return float(position.salary_min)
        elif position.salary_max:
            return float(position.salary_max)
        return 0.0
    
    def _encode_location(self, location: str) -> int:
        """编码地理位置"""
        if not location:
            return 0
        return hash(location) % 1000


class CollaborativeFilteringAlgorithm(BaseRecommendationAlgorithm):
    """协同过滤推荐算法"""
    
    def recommend(self, count: int = 10, **kwargs) -> List[Dict]:
        """基于协同过滤的推荐"""
        try:
            # 获取用户-岗位交互矩阵
            interaction_matrix = self._build_interaction_matrix()
            
            if interaction_matrix.empty:
                return self._fallback_to_popularity(count)
            
            # 计算用户相似度
            user_similarities = self._calculate_user_similarities(interaction_matrix)
            
            # 生成推荐
            recommendations = self._generate_cf_recommendations(
                interaction_matrix, user_similarities, count
            )
            
            return recommendations
            
        except Exception as e:
            logger.error(f"协同过滤推荐失败: {e}")
            return self._fallback_to_popularity(count)
    
    def _build_interaction_matrix(self) -> pd.DataFrame:
        """构建用户-岗位交互矩阵"""
        # 获取所有用户行为数据
        behaviors = UserBehavior.objects.all().values(
            'user_id', 'position_id', 'behavior_type', 'behavior_score'
        )
        
        if not behaviors:
            return pd.DataFrame()
        
        df = pd.DataFrame(behaviors)
        
        # 根据行为类型设置权重
        behavior_weights = {
            'view': 1.0,
            'favorite': 2.0,
            'apply': 3.0,
            'share': 1.5,
            'click': 0.5,
        }
        
        df['weighted_score'] = df.apply(
            lambda row: row['behavior_score'] * behavior_weights.get(row['behavior_type'], 1.0),
            axis=1
        )
        
        # 聚合用户对岗位的总分
        interaction_matrix = df.groupby(['user_id', 'position_id'])['weighted_score'].sum().unstack(fill_value=0)
        
        return interaction_matrix
    
    def _calculate_user_similarities(self, interaction_matrix: pd.DataFrame) -> pd.DataFrame:
        """计算用户相似度"""
        # 使用余弦相似度
        similarity_matrix = cosine_similarity(interaction_matrix)
        
        # 转换为DataFrame
        similarity_df = pd.DataFrame(
            similarity_matrix,
            index=interaction_matrix.index,
            columns=interaction_matrix.index
        )
        
        return similarity_df
    
    def _generate_cf_recommendations(
        self, 
        interaction_matrix: pd.DataFrame, 
        user_similarities: pd.DataFrame, 
        count: int
    ) -> List[Dict]:
        """生成协同过滤推荐"""
        if self.user.id not in interaction_matrix.index:
            return self._fallback_to_popularity(count)
        
        # 获取相似用户
        similar_users = user_similarities.loc[self.user.id].sort_values(ascending=False)
        
        # 排除自己
        similar_users = similar_users.drop(self.user.id, errors='ignore')
        
        # 获取相似用户喜欢的岗位
        recommendations = {}
        
        for similar_user_id, similarity_score in similar_users.head(10).items():
            if similarity_score < 0.1:  # 相似度阈值
                continue
            
            # 获取相似用户的岗位评分
            user_ratings = interaction_matrix.loc[similar_user_id]
            
            for position_id, rating in user_ratings.items():
                if rating > 0 and position_id not in interaction_matrix.loc[self.user.id]:
                    if position_id not in recommendations:
                        recommendations[position_id] = 0
                    recommendations[position_id] += similarity_score * rating
        
        # 排序并获取top N
        sorted_recommendations = sorted(
            recommendations.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:count]
        
        # 转换为结果格式
        results = []
        for position_id, score in sorted_recommendations:
            try:
                position = Position.objects.get(id=position_id, status='active')
                results.append({
                    'position': position,
                    'score': min(score / 10, 1.0),  # 归一化到0-1
                    'reason': f"基于与您相似的用户偏好推荐",
                    'algorithm_type': 'collaborative_filtering',
                    'confidence': min(score / 5, 1.0),
                    'feature_weights': {}
                })
            except Position.DoesNotExist:
                continue
        
        return results
    
    def _fallback_to_popularity(self, count: int) -> List[Dict]:
        """回退到热门推荐"""
        popular_positions = Position.objects.filter(
            status='active'
        ).order_by('-application_count', '-view_count')[:count]
        
        results = []
        for position in popular_positions:
            results.append({
                'position': position,
                'score': 0.5,
                'reason': "热门岗位推荐",
                'algorithm_type': 'popularity',
                'confidence': 0.3,
                'feature_weights': {}
            })
        
        return results


class ContentBasedAlgorithm(BaseRecommendationAlgorithm):
    """基于内容的推荐算法"""
    
    def recommend(self, count: int = 10, **kwargs) -> List[Dict]:
        """基于内容的推荐"""
        try:
            # 获取用户偏好
            user_profile = self._build_user_profile()
            
            # 获取候选岗位
            candidate_positions = self._get_candidate_positions()
            
            if not candidate_positions:
                return []
            
            # 计算岗位匹配度
            recommendations = self._calculate_content_similarity(
                user_profile, candidate_positions, count
            )
            
            return recommendations
            
        except Exception as e:
            logger.error(f"基于内容推荐失败: {e}")
            return []
    
    def _build_user_profile(self) -> Dict:
        """构建用户画像"""
        profile = {
            'education': 0,
            'major': '',
            'location': '',
            'salary_expectation': 0,
            'work_type': 0,
            'preferred_categories': [],
        }
        
        # 从用户基本信息获取
        if hasattr(self.user, 'candidate_profile'):
            candidate = self.user.candidate_profile
            profile['education'] = self._encode_education(candidate.education)
            profile['major'] = candidate.major or ''
            profile['location'] = candidate.preferred_location or ''
            profile['salary_expectation'] = float(candidate.preferred_salary_min or 0)
        
        # 从用户偏好获取
        try:
            preference = UserPreference.objects.get(user=self.user)
            profile['preferred_categories'] = preference.preferred_categories or []
            if preference.min_salary:
                profile['salary_expectation'] = float(preference.min_salary)
        except UserPreference.DoesNotExist:
            pass
        
        # 从用户行为推断偏好
        behaviors = UserBehavior.objects.filter(user=self.user).select_related('position')
        
        category_scores = {}
        location_scores = {}
        
        for behavior in behaviors:
            position = behavior.position
            weight = behavior.behavior_score
            
            # 统计分类偏好
            if position.category_id:
                category_scores[position.category_id] = category_scores.get(position.category_id, 0) + weight
            
            # 统计地点偏好
            if position.location:
                location_scores[position.location] = location_scores.get(position.location, 0) + weight
        
        # 更新偏好
        if category_scores:
            top_category = max(category_scores, key=category_scores.get)
            if top_category not in profile['preferred_categories']:
                profile['preferred_categories'].append(top_category)
        
        if location_scores and not profile['location']:
            profile['location'] = max(location_scores, key=location_scores.get)
        
        return profile
    
    def _get_candidate_positions(self) -> List[Position]:
        """获取候选岗位"""
        # 排除已申请的岗位
        applied_positions = PositionApplication.objects.filter(
            candidate=self.user
        ).values_list('position_id', flat=True)
        
        positions = Position.objects.filter(
            status='active'
        ).exclude(id__in=applied_positions)
        
        return list(positions)
    
    def _calculate_content_similarity(
        self, 
        user_profile: Dict, 
        positions: List[Position], 
        count: int
    ) -> List[Dict]:
        """计算内容相似度"""
        feature_weights = self.recommendation_settings.get('CONTENT_BASED', {}).get('FEATURE_WEIGHTS', {
            'education': 0.3,
            'major': 0.25,
            'experience': 0.2,
            'location': 0.15,
            'salary': 0.1,
        })
        
        recommendations = []
        
        for position in positions:
            similarity_score = 0
            feature_scores = {}
            
            # 学历匹配
            position_education = self._encode_education(position.education_required)
            user_education = user_profile['education']
            
            if position_education <= user_education:
                education_score = 1.0
            else:
                education_score = max(0, 1.0 - (position_education - user_education) * 0.2)
            
            feature_scores['education'] = education_score
            similarity_score += education_score * feature_weights.get('education', 0.3)
            
            # 专业匹配
            major_score = 0
            if user_profile['major'] and position.major_required:
                if user_profile['major'].lower() in position.major_required.lower():
                    major_score = 1.0
                else:
                    major_score = 0.3  # 部分匹配
            else:
                major_score = 0.5  # 无要求时给中等分
            
            feature_scores['major'] = major_score
            similarity_score += major_score * feature_weights.get('major', 0.25)
            
            # 地点匹配
            location_score = 0
            if user_profile['location'] and position.location:
                if user_profile['location'] in position.location or position.location in user_profile['location']:
                    location_score = 1.0
                else:
                    location_score = 0.2
            else:
                location_score = 0.5
            
            feature_scores['location'] = location_score
            similarity_score += location_score * feature_weights.get('location', 0.15)
            
            # 薪资匹配
            salary_score = 0
            if user_profile['salary_expectation'] > 0:
                position_salary = self._calculate_salary_avg(position)
                if position_salary > 0:
                    if position_salary >= user_profile['salary_expectation']:
                        salary_score = 1.0
                    else:
                        salary_score = position_salary / user_profile['salary_expectation']
                else:
                    salary_score = 0.5
            else:
                salary_score = 0.5
            
            feature_scores['salary'] = salary_score
            similarity_score += salary_score * feature_weights.get('salary', 0.1)
            
            # 分类匹配
            category_score = 0
            if position.category_id in user_profile['preferred_categories']:
                category_score = 1.0
            else:
                category_score = 0.3
            
            feature_scores['category'] = category_score
            similarity_score += category_score * 0.2
            
            recommendations.append({
                'position': position,
                'score': min(similarity_score, 1.0),
                'reason': self._generate_content_reason(feature_scores),
                'algorithm_type': 'content_based',
                'confidence': similarity_score,
                'feature_weights': feature_scores
            })
        
        # 排序并返回top N
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:count]
    
    def _generate_content_reason(self, feature_scores: Dict) -> str:
        """生成推荐理由"""
        reasons = []
        
        if feature_scores.get('education', 0) > 0.8:
            reasons.append("学历要求匹配")
        
        if feature_scores.get('major', 0) > 0.8:
            reasons.append("专业对口")
        
        if feature_scores.get('location', 0) > 0.8:
            reasons.append("工作地点符合偏好")
        
        if feature_scores.get('salary', 0) > 0.8:
            reasons.append("薪资待遇理想")
        
        if not reasons:
            reasons.append("综合条件较为匹配")
        
        return "、".join(reasons)


class HybridRecommendationAlgorithm(BaseRecommendationAlgorithm):
    """混合推荐算法"""

    def __init__(self, user: User):
        super().__init__(user)
        self.cf_algorithm = CollaborativeFilteringAlgorithm(user)
        self.content_algorithm = ContentBasedAlgorithm(user)

    def recommend(self, count: int = 10, **kwargs) -> List[Dict]:
        """混合推荐"""
        try:
            # 获取协同过滤推荐
            cf_recommendations = self.cf_algorithm.recommend(count * 2)

            # 获取基于内容的推荐
            content_recommendations = self.content_algorithm.recommend(count * 2)

            # 合并和重新排序
            hybrid_recommendations = self._merge_recommendations(
                cf_recommendations, content_recommendations, count
            )

            return hybrid_recommendations

        except Exception as e:
            logger.error(f"混合推荐失败: {e}")
            return self._fallback_to_popularity(count)

    def _merge_recommendations(
        self,
        cf_recs: List[Dict],
        content_recs: List[Dict],
        count: int
    ) -> List[Dict]:
        """合并推荐结果"""
        # 权重设置
        cf_weight = 0.6
        content_weight = 0.4

        # 创建岗位ID到推荐的映射
        position_recommendations = {}

        # 处理协同过滤推荐
        for rec in cf_recs:
            position_id = rec['position'].id
            position_recommendations[position_id] = {
                'position': rec['position'],
                'cf_score': rec['score'],
                'content_score': 0,
                'cf_reason': rec['reason'],
                'content_reason': '',
                'feature_weights': rec.get('feature_weights', {})
            }

        # 处理基于内容的推荐
        for rec in content_recs:
            position_id = rec['position'].id
            if position_id in position_recommendations:
                position_recommendations[position_id]['content_score'] = rec['score']
                position_recommendations[position_id]['content_reason'] = rec['reason']
                position_recommendations[position_id]['feature_weights'].update(
                    rec.get('feature_weights', {})
                )
            else:
                position_recommendations[position_id] = {
                    'position': rec['position'],
                    'cf_score': 0,
                    'content_score': rec['score'],
                    'cf_reason': '',
                    'content_reason': rec['reason'],
                    'feature_weights': rec.get('feature_weights', {})
                }

        # 计算混合分数
        hybrid_results = []
        for position_id, rec_data in position_recommendations.items():
            hybrid_score = (
                rec_data['cf_score'] * cf_weight +
                rec_data['content_score'] * content_weight
            )

            # 生成混合推荐理由
            reasons = []
            if rec_data['cf_reason']:
                reasons.append(rec_data['cf_reason'])
            if rec_data['content_reason']:
                reasons.append(rec_data['content_reason'])

            hybrid_reason = "；".join(reasons) if reasons else "综合推荐"

            hybrid_results.append({
                'position': rec_data['position'],
                'score': hybrid_score,
                'reason': hybrid_reason,
                'algorithm_type': 'hybrid',
                'confidence': hybrid_score,
                'feature_weights': rec_data['feature_weights']
            })

        # 排序并返回top N
        hybrid_results.sort(key=lambda x: x['score'], reverse=True)
        return hybrid_results[:count]


class PopularityRecommendationAlgorithm(BaseRecommendationAlgorithm):
    """热门推荐算法"""

    def recommend(self, count: int = 10, **kwargs) -> List[Dict]:
        """热门推荐"""
        # 排除已申请的岗位
        applied_positions = PositionApplication.objects.filter(
            candidate=self.user
        ).values_list('position_id', flat=True)

        # 获取热门岗位
        popular_positions = Position.objects.filter(
            status='active'
        ).exclude(
            id__in=applied_positions
        ).order_by(
            '-is_featured', '-application_count', '-view_count', '-favorite_count'
        )[:count]

        results = []
        for i, position in enumerate(popular_positions):
            # 计算热门度分数
            popularity_score = self._calculate_popularity_score(position)

            results.append({
                'position': position,
                'score': popularity_score,
                'reason': self._generate_popularity_reason(position),
                'algorithm_type': 'popularity',
                'confidence': 0.5,
                'feature_weights': {}
            })

        return results

    def _calculate_popularity_score(self, position: Position) -> float:
        """计算热门度分数"""
        # 基础分数
        score = 0.3

        # 推荐岗位加分
        if position.is_featured:
            score += 0.3

        # 紧急岗位加分
        if position.is_urgent:
            score += 0.2

        # 申请人数加分（归一化）
        if position.application_count > 0:
            score += min(position.application_count / 100, 0.2)

        # 浏览次数加分（归一化）
        if position.view_count > 0:
            score += min(position.view_count / 1000, 0.1)

        # 收藏次数加分（归一化）
        if position.favorite_count > 0:
            score += min(position.favorite_count / 50, 0.1)

        return min(score, 1.0)

    def _generate_popularity_reason(self, position: Position) -> str:
        """生成热门推荐理由"""
        reasons = []

        if position.is_featured:
            reasons.append("推荐岗位")

        if position.is_urgent:
            reasons.append("紧急招聘")

        if position.application_count > 20:
            reasons.append("热门岗位")

        if position.view_count > 100:
            reasons.append("关注度高")

        if not reasons:
            reasons.append("优质岗位")

        return "、".join(reasons)


class RecommendationService:
    """推荐服务"""

    def __init__(self):
        self.algorithms = {
            'collaborative_filtering': CollaborativeFilteringAlgorithm,
            'content_based': ContentBasedAlgorithm,
            'hybrid': HybridRecommendationAlgorithm,
            'popularity': PopularityRecommendationAlgorithm,
        }

    def get_recommendations(
        self,
        user: User,
        algorithm_type: str = 'hybrid',
        count: int = 10,
        **kwargs
    ) -> List[Dict]:
        """获取推荐结果"""
        if algorithm_type not in self.algorithms:
            algorithm_type = 'hybrid'

        algorithm_class = self.algorithms[algorithm_type]
        algorithm = algorithm_class(user)

        return algorithm.recommend(count, **kwargs)

    def save_recommendations(
        self,
        user: User,
        recommendations: List[Dict],
        batch_id: str = None
    ) -> List[RecommendationRecord]:
        """保存推荐记录"""
        if not batch_id:
            import uuid
            batch_id = str(uuid.uuid4())

        records = []
        for rec in recommendations:
            record = RecommendationRecord.objects.create(
                user=user,
                position=rec['position'],
                algorithm_type=rec['algorithm_type'],
                recommendation_score=rec['score'],
                confidence_score=rec.get('confidence', 0),
                reason=rec['reason'],
                feature_weights=rec.get('feature_weights', {}),
                batch_id=batch_id
            )
            records.append(record)

        return records

    def update_user_behavior(
        self,
        user: User,
        position: Position,
        behavior_type: str,
        behavior_score: float = 1.0,
        **kwargs
    ):
        """更新用户行为"""
        UserBehavior.objects.create(
            user=user,
            position=position,
            behavior_type=behavior_type,
            behavior_score=behavior_score,
            **kwargs
        )

    def get_user_recommendations_history(
        self,
        user: User,
        limit: int = 50
    ) -> List[RecommendationRecord]:
        """获取用户推荐历史"""
        return RecommendationRecord.objects.filter(
            user=user
        ).select_related('position').order_by('-recommended_at')[:limit]


# 创建全局推荐服务实例
recommendation_service = RecommendationService()
