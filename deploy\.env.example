# 黔南州考试岗位推荐系统环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# ===========================================
# 基础配置
# ===========================================

# 环境类型 (development/staging/production)
ENVIRONMENT=production

# 调试模式 (True/False)
DEBUG=False

# 允许的主机列表 (用逗号分隔)
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# CORS允许的源 (用逗号分隔)
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://admin.your-domain.com

# 密钥 (请生成一个强密钥)
SECRET_KEY=your-very-secret-key-here-change-this-in-production

# ===========================================
# 数据库配置
# ===========================================

# MySQL配置
MYSQL_ROOT_PASSWORD=your-strong-root-password
MYSQL_DATABASE=qiannan_exam
MYSQL_USER=qiannan_user
MYSQL_PASSWORD=your-strong-mysql-password
MYSQL_PORT=3306

# 数据库连接URL (如果使用其他数据库)
DATABASE_URL=mysql://qiannan_user:your-strong-mysql-password@mysql:3306/qiannan_exam

# ===========================================
# Redis配置
# ===========================================

# Redis密码
REDIS_PASSWORD=your-strong-redis-password
REDIS_PORT=6379

# Redis连接URL
REDIS_URL=redis://:your-strong-redis-password@redis:6379/0

# ===========================================
# Celery配置
# ===========================================

# Celery Broker URL
CELERY_BROKER_URL=redis://:your-strong-redis-password@redis:6379/0

# Celery Result Backend
CELERY_RESULT_BACKEND=redis://:your-strong-redis-password@redis:6379/1

# ===========================================
# 邮件配置
# ===========================================

# 邮件后端
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend

# SMTP服务器配置
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# 默认发件人
DEFAULT_FROM_EMAIL=黔南州考试系统 <<EMAIL>>

# ===========================================
# 文件存储配置
# ===========================================

# 媒体文件URL前缀
MEDIA_URL=/media/

# 静态文件URL前缀
STATIC_URL=/static/

# 文件上传大小限制 (MB)
FILE_UPLOAD_MAX_MEMORY_SIZE=10

# ===========================================
# 安全配置
# ===========================================

# HTTPS相关
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Cookie安全
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
CSRF_COOKIE_HTTPONLY=True

# ===========================================
# 第三方服务配置
# ===========================================

# 邮件通知配置
EMAIL_NOTIFICATIONS=True

# 短信服务配置 (可选)
SMS_ACCESS_KEY_ID=your-sms-access-key
SMS_ACCESS_KEY_SECRET=your-sms-secret
SMS_SIGN_NAME=黔南州考试系统
SMS_TEMPLATE_CODE=SMS_123456789

# 对象存储配置 (可选，用于文件存储)
OSS_ACCESS_KEY_ID=your-oss-access-key
OSS_ACCESS_KEY_SECRET=your-oss-secret
OSS_BUCKET_NAME=qiannan-exam-files
OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com

# ===========================================
# 监控和日志配置
# ===========================================

# 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL=INFO

# Sentry错误监控 (可选)
SENTRY_DSN=your-sentry-dsn-url

# ===========================================
# 端口配置
# ===========================================

# 应用端口
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 数据库端口
MYSQL_PORT=3306
REDIS_PORT=6379

# Web服务器端口
HTTP_PORT=80
HTTPS_PORT=443

# 监控服务端口 (可选)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin123

# ===========================================
# 前端配置
# ===========================================

# Vue.js应用配置
VUE_APP_BASE_API=/api/v1
VUE_APP_TITLE=黔南州考试岗位推荐系统

# 用户端配置
USER_PORTAL_URL=https://your-domain.com

# ===========================================
# 备份配置
# ===========================================

# 数据库备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份存储路径
BACKUP_PATH=/backup

# 自动备份时间 (cron格式)
BACKUP_SCHEDULE=0 2 * * *

# ===========================================
# 性能配置
# ===========================================

# 数据库连接池大小
DB_CONN_MAX_AGE=600

# 缓存超时时间 (秒)
CACHE_TIMEOUT=3600

# 会话超时时间 (秒)
SESSION_COOKIE_AGE=86400

# ===========================================
# 开发配置 (仅开发环境使用)
# ===========================================

# 开发服务器配置
DEV_SERVER_HOST=0.0.0.0
DEV_SERVER_PORT=8000

# 热重载
HOT_RELOAD=True

# 调试工具栏
DEBUG_TOOLBAR=False
