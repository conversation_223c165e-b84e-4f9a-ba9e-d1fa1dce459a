"""
考试管理应用URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import ExamViewSet, ExamRegistrationViewSet, AdmissionTicketViewSet, ScoreViewSet

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'exams', ExamViewSet)
router.register(r'registrations', ExamRegistrationViewSet)
router.register(r'admission-tickets', AdmissionTicketViewSet)
router.register(r'scores', ScoreViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
