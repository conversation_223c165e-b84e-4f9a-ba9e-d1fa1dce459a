"""
推荐系统管理后台配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count, Avg
from .models import (
    UserBehavior, UserPreference, RecommendationRecord, 
    RecommendationFeedback, UserSimilarity, PositionSimilarity
)


@admin.register(UserBehavior)
class UserBehaviorAdmin(admin.ModelAdmin):
    """用户行为管理"""
    
    list_display = [
        'user_name', 'position_title', 'behavior_type', 'behavior_score',
        'ip_address', 'created_at'
    ]
    list_filter = ['behavior_type', 'created_at']
    search_fields = [
        'user__username', 'user__real_name', 'position__title', 'ip_address'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['user', 'position', 'behavior_type', 'behavior_score']
        }),
        ('上下文信息', {
            'fields': ['session_id', 'ip_address', 'user_agent', 'referrer']
        }),
        ('额外数据', {
            'fields': ['extra_data'],
            'classes': ['collapse']
        }),
        ('时间信息', {
            'fields': ['created_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at']
    
    def user_name(self, obj):
        """用户姓名"""
        return obj.user.real_name or obj.user.username
    user_name.short_description = '用户'
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'position')
    
    def has_add_permission(self, request):
        """禁止手动添加行为记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改行为记录"""
        return False


@admin.register(UserPreference)
class UserPreferenceAdmin(admin.ModelAdmin):
    """用户偏好管理"""
    
    list_display = [
        'user_name', 'min_salary', 'max_salary', 'enable_recommendations',
        'recommendation_frequency', 'updated_at'
    ]
    list_filter = ['enable_recommendations', 'recommendation_frequency', 'updated_at']
    search_fields = ['user__username', 'user__real_name']
    ordering = ['-updated_at']
    
    fieldsets = [
        ('用户信息', {
            'fields': ['user']
        }),
        ('地理偏好', {
            'fields': ['preferred_provinces', 'preferred_cities']
        }),
        ('岗位偏好', {
            'fields': ['preferred_categories', 'preferred_departments']
        }),
        ('薪资偏好', {
            'fields': ['min_salary', 'max_salary']
        }),
        ('工作类型偏好', {
            'fields': ['preferred_work_types']
        }),
        ('推荐设置', {
            'fields': ['enable_recommendations', 'recommendation_frequency']
        }),
        ('时间信息', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at']
    
    def user_name(self, obj):
        """用户姓名"""
        return obj.user.real_name or obj.user.username
    user_name.short_description = '用户'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(RecommendationRecord)
class RecommendationRecordAdmin(admin.ModelAdmin):
    """推荐记录管理"""
    
    list_display = [
        'user_name', 'position_title', 'algorithm_type', 'recommendation_score',
        'status', 'user_rating', 'recommended_at'
    ]
    list_filter = ['algorithm_type', 'status', 'recommended_at']
    search_fields = [
        'user__username', 'user__real_name', 'position__title', 'batch_id'
    ]
    ordering = ['-recommended_at']
    date_hierarchy = 'recommended_at'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['user', 'position', 'algorithm_type']
        }),
        ('推荐信息', {
            'fields': [
                'recommendation_score', 'confidence_score', 'reason', 'feature_weights'
            ]
        }),
        ('状态信息', {
            'fields': ['status', 'batch_id']
        }),
        ('用户反馈', {
            'fields': ['user_rating', 'user_feedback']
        }),
        ('时间信息', {
            'fields': ['recommended_at', 'clicked_at', 'expires_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['recommended_at', 'clicked_at']
    
    def user_name(self, obj):
        """用户姓名"""
        return obj.user.real_name or obj.user.username
    user_name.short_description = '用户'
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.position.title
    position_title.short_description = '岗位'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'position')
    
    def has_add_permission(self, request):
        """禁止手动添加推荐记录"""
        return False
    
    actions = ['mark_as_expired']
    
    def mark_as_expired(self, request, queryset):
        """标记为已过期"""
        updated = queryset.update(status='expired')
        self.message_user(request, f"已将 {updated} 条推荐记录标记为过期")
    mark_as_expired.short_description = "标记为已过期"


@admin.register(RecommendationFeedback)
class RecommendationFeedbackAdmin(admin.ModelAdmin):
    """推荐反馈管理"""
    
    list_display = [
        'user_name', 'position_title', 'feedback_type', 'rating', 'created_at'
    ]
    list_filter = ['feedback_type', 'rating', 'created_at']
    search_fields = [
        'user__username', 'user__real_name', 'recommendation__position__title'
    ]
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
    
    fieldsets = [
        ('基本信息', {
            'fields': ['recommendation', 'user']
        }),
        ('反馈信息', {
            'fields': ['feedback_type', 'rating', 'comment']
        }),
        ('时间信息', {
            'fields': ['created_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at']
    
    def user_name(self, obj):
        """用户姓名"""
        return obj.user.real_name or obj.user.username
    user_name.short_description = '用户'
    
    def position_title(self, obj):
        """岗位标题"""
        return obj.recommendation.position.title
    position_title.short_description = '岗位'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'recommendation__position'
        )
    
    def has_add_permission(self, request):
        """禁止手动添加反馈记录"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改反馈记录"""
        return False


@admin.register(UserSimilarity)
class UserSimilarityAdmin(admin.ModelAdmin):
    """用户相似度管理"""
    
    list_display = [
        'user1_name', 'user2_name', 'similarity_score', 'algorithm_type', 'calculated_at'
    ]
    list_filter = ['algorithm_type', 'calculated_at']
    search_fields = [
        'user1__username', 'user1__real_name', 'user2__username', 'user2__real_name'
    ]
    ordering = ['-similarity_score']
    
    fieldsets = [
        ('用户信息', {
            'fields': ['user1', 'user2']
        }),
        ('相似度信息', {
            'fields': ['similarity_score', 'algorithm_type']
        }),
        ('计算信息', {
            'fields': ['calculated_at', 'data_version'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at']
    
    def user1_name(self, obj):
        """用户1姓名"""
        return obj.user1.real_name or obj.user1.username
    user1_name.short_description = '用户1'
    
    def user2_name(self, obj):
        """用户2姓名"""
        return obj.user2.real_name or obj.user2.username
    user2_name.short_description = '用户2'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user1', 'user2')
    
    def has_add_permission(self, request):
        """禁止手动添加相似度记录"""
        return False


@admin.register(PositionSimilarity)
class PositionSimilarityAdmin(admin.ModelAdmin):
    """岗位相似度管理"""
    
    list_display = [
        'position1_title', 'position2_title', 'similarity_score', 'algorithm_type', 'calculated_at'
    ]
    list_filter = ['algorithm_type', 'calculated_at']
    search_fields = [
        'position1__title', 'position2__title'
    ]
    ordering = ['-similarity_score']
    
    fieldsets = [
        ('岗位信息', {
            'fields': ['position1', 'position2']
        }),
        ('相似度信息', {
            'fields': ['similarity_score', 'algorithm_type', 'feature_similarities']
        }),
        ('计算信息', {
            'fields': ['calculated_at', 'data_version'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['calculated_at']
    
    def position1_title(self, obj):
        """岗位1标题"""
        return obj.position1.title
    position1_title.short_description = '岗位1'
    
    def position2_title(self, obj):
        """岗位2标题"""
        return obj.position2.title
    position2_title.short_description = '岗位2'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('position1', 'position2')
    
    def has_add_permission(self, request):
        """禁止手动添加相似度记录"""
        return False
