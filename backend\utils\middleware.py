"""
自定义中间件
"""
import json
import logging
import time
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志中间件"""
    
    def process_request(self, request):
        """记录请求开始时间"""
        request.start_time = time.time()
        
        # 记录请求信息
        logger.info(f"请求开始: {request.method} {request.path}", extra={
            'method': request.method,
            'path': request.path,
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
            'ip_address': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        })
    
    def process_response(self, request, response):
        """记录请求结束信息"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info(f"请求结束: {request.method} {request.path}", extra={
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration': duration,
                'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
            })
        
        return response
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """JWT认证中间件"""
    
    def process_request(self, request):
        """处理JWT认证"""
        # 跳过不需要认证的路径
        skip_paths = [
            '/api/v1/users/auth/login/',
            '/api/v1/users/auth/register/',
            '/api/v1/users/auth/wechat-login/',
            '/api/v1/users/auth/refresh/',
            '/admin/',
            '/swagger/',
            '/redoc/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # 尝试JWT认证
        jwt_auth = JWTAuthentication()
        try:
            auth_result = jwt_auth.authenticate(request)
            if auth_result:
                request.user, request.auth = auth_result
            else:
                request.user = AnonymousUser()
        except (InvalidToken, TokenError) as e:
            # JWT令牌无效，返回401错误
            return JsonResponse({
                'code': 401,
                'message': '认证失败，请重新登录',
                'success': False,
                'data': None
            }, status=401)
        except Exception as e:
            logger.error(f"JWT认证异常: {e}")
            request.user = AnonymousUser()
        
        return None


class ExceptionHandlingMiddleware(MiddlewareMixin):
    """异常处理中间件"""
    
    def process_exception(self, request, exception):
        """处理未捕获的异常"""
        logger.error(f"未处理异常: {exception}", exc_info=True, extra={
            'method': request.method,
            'path': request.path,
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') and request.user.is_authenticated else None,
        })
        
        # 返回统一的错误响应
        return JsonResponse({
            'code': 500,
            'message': '服务器内部错误',
            'success': False,
            'data': None
        }, status=500)


class SecurityHeadersMiddleware(MiddlewareMixin):
    """安全头中间件"""
    
    def process_response(self, request, response):
        """添加安全头"""
        # 防止点击劫持
        response['X-Frame-Options'] = 'DENY'
        
        # 防止MIME类型嗅探
        response['X-Content-Type-Options'] = 'nosniff'
        
        # XSS保护
        response['X-XSS-Protection'] = '1; mode=block'
        
        # 引用策略
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 内容安全策略（根据需要调整）
        response['Content-Security-Policy'] = "default-src 'self'"
        
        return response


class APIVersionMiddleware(MiddlewareMixin):
    """API版本中间件"""
    
    def process_request(self, request):
        """处理API版本"""
        # 从URL路径中提取版本信息
        if request.path.startswith('/api/'):
            path_parts = request.path.split('/')
            if len(path_parts) >= 3 and path_parts[2].startswith('v'):
                request.api_version = path_parts[2]
            else:
                request.api_version = 'v1'  # 默认版本
        
        return None


class RateLimitMiddleware(MiddlewareMixin):
    """简单的限流中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limit_cache = {}  # 简单的内存缓存，生产环境应使用Redis
        super().__init__(get_response)
    
    def process_request(self, request):
        """检查请求频率"""
        # 获取客户端标识
        client_id = self.get_client_id(request)
        current_time = time.time()
        
        # 清理过期记录
        self.cleanup_expired_records(current_time)
        
        # 检查请求频率
        if client_id in self.rate_limit_cache:
            requests = self.rate_limit_cache[client_id]
            # 过去1分钟内的请求数
            recent_requests = [t for t in requests if current_time - t < 60]
            
            if len(recent_requests) >= 100:  # 每分钟最多100个请求
                return JsonResponse({
                    'code': 429,
                    'message': '请求过于频繁，请稍后再试',
                    'success': False,
                    'data': None
                }, status=429)
            
            # 更新请求记录
            recent_requests.append(current_time)
            self.rate_limit_cache[client_id] = recent_requests
        else:
            self.rate_limit_cache[client_id] = [current_time]
        
        return None
    
    def get_client_id(self, request):
        """获取客户端标识"""
        # 优先使用用户ID，其次使用IP地址
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user_{request.user.id}"
        else:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            return f"ip_{ip}"
    
    def cleanup_expired_records(self, current_time):
        """清理过期的请求记录"""
        for client_id in list(self.rate_limit_cache.keys()):
            requests = self.rate_limit_cache[client_id]
            # 保留过去1小时内的记录
            recent_requests = [t for t in requests if current_time - t < 3600]
            if recent_requests:
                self.rate_limit_cache[client_id] = recent_requests
            else:
                del self.rate_limit_cache[client_id]


class CacheControlMiddleware(MiddlewareMixin):
    """缓存控制中间件"""
    
    def process_response(self, request, response):
        """设置缓存控制头"""
        # API响应不缓存
        if request.path.startswith('/api/'):
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
        
        # 静态文件缓存1天
        elif request.path.startswith('/static/') or request.path.startswith('/media/'):
            response['Cache-Control'] = 'public, max-age=86400'
        
        return response
