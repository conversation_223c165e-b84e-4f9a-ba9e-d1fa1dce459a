// pages/index/index.js
import Toast from '@vant/weapp/toast/toast'
import api from '../../api/index'

const app = getApp()

Page({
  data: {
    loading: false,
    isLogin: false,
    banners: [],
    latestExams: [],
    hotPositions: [],
    recommendations: [],
    notices: []
  },

  onLoad() {
    console.log('首页加载')
    this.checkLoginStatus()
    this.loadPageData()
  },

  onShow() {
    console.log('首页显示')
    // 每次显示时检查登录状态
    this.checkLoginStatus()
    
    // 如果已登录，刷新推荐数据
    if (this.data.isLogin) {
      this.loadRecommendations()
    }
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLogin = app.globalData.isLogin
    this.setData({ isLogin })
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadLatestExams(),
        this.loadHotPositions(),
        this.loadNotices()
      ])
      
      // 如果已登录，加载推荐数据
      if (this.data.isLogin) {
        await this.loadRecommendations()
      }
    } catch (error) {
      console.error('加载页面数据失败:', error)
      Toast.fail('加载失败，请稍后重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载轮播图数据
  async loadBanners() {
    try {
      // 这里可以从后端获取轮播图数据，暂时使用模拟数据
      const banners = [
        {
          id: 1,
          image: '/images/banner1.jpg',
          title: '2024年黔南州公务员考试',
          url: '/pages/exams/detail/detail?id=1'
        },
        {
          id: 2,
          image: '/images/banner2.jpg',
          title: '事业单位招聘',
          url: '/pages/positions/list/list'
        },
        {
          id: 3,
          image: '/images/banner3.jpg',
          title: '智能岗位推荐',
          url: '/pages/recommendations/list/list'
        }
      ]
      
      this.setData({ banners })
    } catch (error) {
      console.error('加载轮播图失败:', error)
    }
  },

  // 加载最新考试
  async loadLatestExams() {
    try {
      const response = await api.exam.getExamList({
        page: 1,
        page_size: 3,
        ordering: '-created_at'
      })
      
      const latestExams = response.results.map(exam => ({
        ...exam,
        registration_start: app.utils.formatDate(exam.registration_start),
        registration_end: app.utils.formatDate(exam.registration_end)
      }))
      
      this.setData({ latestExams })
    } catch (error) {
      console.error('加载最新考试失败:', error)
    }
  },

  // 加载热门岗位
  async loadHotPositions() {
    try {
      const response = await api.position.getPositionList({
        page: 1,
        page_size: 5,
        ordering: '-application_count,-view_count'
      })
      
      this.setData({ hotPositions: response.results })
    } catch (error) {
      console.error('加载热门岗位失败:', error)
    }
  },

  // 加载个性化推荐
  async loadRecommendations() {
    try {
      const response = await api.recommendation.generateRecommendations({
        algorithm_type: 'hybrid',
        count: 3
      })
      
      const recommendations = response.map(item => ({
        ...item,
        score: Math.round(item.score * 100)
      }))
      
      this.setData({ recommendations })
    } catch (error) {
      console.error('加载推荐数据失败:', error)
    }
  },

  // 加载系统公告
  async loadNotices() {
    try {
      const response = await api.common.getNotices({
        page: 1,
        page_size: 3
      })
      
      const notices = response.results.map(notice => ({
        ...notice,
        created_at: app.utils.formatDate(notice.created_at, 'MM-DD')
      }))
      
      this.setData({ notices })
    } catch (error) {
      console.error('加载公告失败:', error)
    }
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item
    if (item.url) {
      wx.navigateTo({
        url: item.url
      })
    }
  },

  // 考试点击事件
  onExamTap(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/exams/detail/detail?id=${id}`
    })
  },

  // 岗位点击事件
  onPositionTap(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/positions/detail/detail?id=${id}`
    })
  },

  // 推荐点击事件
  onRecommendationTap(e) {
    const item = e.currentTarget.dataset.item
    
    // 记录点击行为
    api.recommendation.clickRecommendation(item.record_id).catch(console.error)
    
    // 跳转到岗位详情
    wx.navigateTo({
      url: `/pages/positions/detail/detail?id=${item.position.id}`
    })
  },

  // 公告点击事件
  onNoticeTap(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/common/notice/notice?id=${id}`
    })
  },

  // 导航到考试页面
  navigateToExams() {
    wx.switchTab({
      url: '/pages/exams/list/list'
    })
  },

  // 导航到岗位页面
  navigateToPositions() {
    wx.switchTab({
      url: '/pages/positions/list/list'
    })
  },

  // 导航到成绩查询
  navigateToScores() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }
    
    wx.navigateTo({
      url: '/pages/exams/score/score'
    })
  },

  // 导航到推荐页面
  navigateToRecommendations() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }
    
    wx.switchTab({
      url: '/pages/recommendations/list/list'
    })
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  }
})
