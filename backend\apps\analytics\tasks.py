"""
数据分析定时任务
"""
import logging
from datetime import timedelta
from django.utils import timezone
from django.db.models import Count, Av<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Q
from celery import shared_task

from apps.users.models import User
from apps.exams.models import Exam, ExamRegistration, Score
from apps.positions.models import Position, PositionApplication, PositionView, PositionFavorite
from apps.recommendations.models import RecommendationRecord, UserBehavior, RecommendationFeedback
from .models import (
    SystemStatistics, ExamAnalytics, PositionAnalytics, 
    UserAnalytics, RecommendationAnalytics
)

logger = logging.getLogger(__name__)


@shared_task
def generate_daily_statistics():
    """生成每日系统统计"""
    logger.info("开始生成每日系统统计")
    
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    
    try:
        # 用户统计
        total_users = User.objects.count()
        new_users = User.objects.filter(date_joined__date=yesterday).count()
        active_users = User.objects.filter(last_login__date=yesterday).count()
        candidate_users = User.objects.filter(user_type='candidate').count()
        recruiter_users = User.objects.filter(user_type='recruiter').count()
        
        # 考试统计
        total_exams = Exam.objects.count()
        new_exams = Exam.objects.filter(created_at__date=yesterday).count()
        ongoing_exams = Exam.objects.filter(
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).count()
        total_registrations = ExamRegistration.objects.count()
        new_registrations = ExamRegistration.objects.filter(created_at__date=yesterday).count()
        
        # 岗位统计
        total_positions = Position.objects.count()
        new_positions = Position.objects.filter(created_at__date=yesterday).count()
        active_positions = Position.objects.filter(status='active').count()
        total_applications = PositionApplication.objects.count()
        new_applications = PositionApplication.objects.filter(applied_at__date=yesterday).count()
        
        # 推荐统计
        total_recommendations = RecommendationRecord.objects.count()
        new_recommendations = RecommendationRecord.objects.filter(recommended_at__date=yesterday).count()
        recommendation_clicks = RecommendationRecord.objects.filter(
            clicked_at__date=yesterday
        ).count()
        recommendation_applications = RecommendationRecord.objects.filter(
            status='applied',
            recommended_at__date=yesterday
        ).count()
        
        # 创建或更新统计记录
        stats, created = SystemStatistics.objects.update_or_create(
            stat_type='daily',
            stat_date=yesterday,
            defaults={
                'total_users': total_users,
                'new_users': new_users,
                'active_users': active_users,
                'candidate_users': candidate_users,
                'recruiter_users': recruiter_users,
                'total_exams': total_exams,
                'new_exams': new_exams,
                'ongoing_exams': ongoing_exams,
                'total_registrations': total_registrations,
                'new_registrations': new_registrations,
                'total_positions': total_positions,
                'new_positions': new_positions,
                'active_positions': active_positions,
                'total_applications': total_applications,
                'new_applications': new_applications,
                'total_recommendations': total_recommendations,
                'new_recommendations': new_recommendations,
                'recommendation_clicks': recommendation_clicks,
                'recommendation_applications': recommendation_applications,
            }
        )
        
        logger.info(f"每日系统统计生成完成: {'创建' if created else '更新'}")
        return {'status': 'success', 'created': created}
        
    except Exception as e:
        logger.error(f"生成每日系统统计失败: {e}")
        return {'status': 'error', 'message': str(e)}


@shared_task
def generate_exam_analytics():
    """生成考试分析数据"""
    logger.info("开始生成考试分析数据")
    
    success_count = 0
    error_count = 0
    
    # 获取需要分析的考试（有成绩数据的考试）
    exams_with_scores = Exam.objects.filter(scores__isnull=False).distinct()
    
    for exam in exams_with_scores:
        try:
            # 报名统计
            registrations = exam.registrations.all()
            registration_count = registrations.count()
            approved_registration_count = registrations.filter(status='approved').count()
            
            # 成绩统计
            scores = exam.scores.all()
            score_count = scores.count()
            qualified_count = scores.filter(is_qualified=True).count()
            
            # 计算率
            attendance_rate = round(score_count / approved_registration_count * 100, 2) if approved_registration_count > 0 else 0
            qualification_rate = round(qualified_count / score_count * 100, 2) if score_count > 0 else 0
            
            # 成绩统计
            score_stats = scores.aggregate(
                avg_initial=Avg('initial_score'),
                avg_assessment=Avg('assessment_score'),
                avg_total=Avg('total_score'),
                max_total=Max('total_score'),
                min_total=Min('total_score')
            )
            
            # 分数段分布
            score_distribution = []
            score_ranges = [(90, 100), (80, 89), (70, 79), (60, 69), (0, 59)]
            for min_score, max_score in score_ranges:
                count = scores.filter(
                    total_score__gte=min_score,
                    total_score__lte=max_score
                ).count()
                score_distribution.append({
                    'range': f"{min_score}-{max_score}",
                    'count': count
                })
            
            # 地区分布（基于考生地址）
            region_distribution = []
            regions = scores.values(
                'candidate__candidate_profile__address'
            ).annotate(count=Count('id')).order_by('-count')[:10]
            
            for region in regions:
                if region['candidate__candidate_profile__address']:
                    region_distribution.append({
                        'region': region['candidate__candidate_profile__address'],
                        'count': region['count']
                    })
            
            # 学历分布
            education_distribution = []
            educations = scores.values(
                'candidate__candidate_profile__education'
            ).annotate(count=Count('id'))
            
            for education in educations:
                if education['candidate__candidate_profile__education']:
                    education_distribution.append({
                        'education': education['candidate__candidate_profile__education'],
                        'count': education['count']
                    })
            
            # 年龄分布
            age_distribution = []
            # 这里简化处理，实际应该计算年龄段
            
            # 创建或更新分析记录
            analytics, created = ExamAnalytics.objects.update_or_create(
                exam=exam,
                defaults={
                    'registration_count': registration_count,
                    'approved_registration_count': approved_registration_count,
                    'participant_count': score_count,
                    'attendance_rate': attendance_rate,
                    'score_count': score_count,
                    'qualified_count': qualified_count,
                    'qualification_rate': qualification_rate,
                    'avg_initial_score': score_stats['avg_initial'],
                    'avg_assessment_score': score_stats['avg_assessment'],
                    'avg_total_score': score_stats['avg_total'],
                    'max_total_score': score_stats['max_total'],
                    'min_total_score': score_stats['min_total'],
                    'score_distribution': score_distribution,
                    'region_distribution': region_distribution,
                    'education_distribution': education_distribution,
                    'age_distribution': age_distribution,
                }
            )
            
            success_count += 1
            
        except Exception as e:
            logger.error(f"生成考试 {exam.id} 分析数据失败: {e}")
            error_count += 1
    
    logger.info(f"考试分析数据生成完成，成功: {success_count}，失败: {error_count}")
    return {'success': success_count, 'error': error_count}


@shared_task
def generate_position_analytics():
    """生成岗位分析数据"""
    logger.info("开始生成岗位分析数据")
    
    success_count = 0
    error_count = 0
    
    # 获取有活动的岗位
    active_positions = Position.objects.filter(
        Q(applications__isnull=False) | Q(views__isnull=False) | Q(favorites__isnull=False)
    ).distinct()
    
    for position in active_positions:
        try:
            # 浏览统计
            views = position.views.all()
            total_views = views.count()
            unique_views = views.values('user').distinct().count()
            today = timezone.now().date()
            daily_views = views.filter(viewed_at__date=today).count()
            
            # 申请统计
            applications = position.applications.all()
            total_applications = applications.count()
            pending_applications = applications.filter(status='pending').count()
            approved_applications = applications.filter(status='approved').count()
            rejected_applications = applications.filter(status='rejected').count()
            
            # 转化率
            view_to_application_rate = round(total_applications / total_views * 100, 2) if total_views > 0 else 0
            application_approval_rate = round(approved_applications / total_applications * 100, 2) if total_applications > 0 else 0
            
            # 收藏统计
            total_favorites = position.favorites.count()
            
            # 竞争比例
            competition_ratio = round(total_applications / position.recruitment_count, 2) if position.recruitment_count > 0 else 0
            
            # 申请人分析
            applicant_education_distribution = list(
                applications.values(
                    'candidate__candidate_profile__education'
                ).annotate(count=Count('id'))
            )
            
            # 推荐统计
            recommendations = RecommendationRecord.objects.filter(position=position)
            recommendation_count = recommendations.count()
            recommendation_click_count = recommendations.filter(status='clicked').count()
            recommendation_application_count = recommendations.filter(status='applied').count()
            
            # 创建或更新分析记录
            analytics, created = PositionAnalytics.objects.update_or_create(
                position=position,
                defaults={
                    'total_views': total_views,
                    'unique_views': unique_views,
                    'daily_views': daily_views,
                    'total_applications': total_applications,
                    'pending_applications': pending_applications,
                    'approved_applications': approved_applications,
                    'rejected_applications': rejected_applications,
                    'view_to_application_rate': view_to_application_rate,
                    'application_approval_rate': application_approval_rate,
                    'total_favorites': total_favorites,
                    'competition_ratio': competition_ratio,
                    'applicant_education_distribution': applicant_education_distribution,
                    'recommendation_count': recommendation_count,
                    'recommendation_click_count': recommendation_click_count,
                    'recommendation_application_count': recommendation_application_count,
                }
            )
            
            success_count += 1
            
        except Exception as e:
            logger.error(f"生成岗位 {position.id} 分析数据失败: {e}")
            error_count += 1
    
    logger.info(f"岗位分析数据生成完成，成功: {success_count}，失败: {error_count}")
    return {'success': success_count, 'error': error_count}


@shared_task
def generate_user_analytics():
    """生成用户分析数据"""
    logger.info("开始生成用户分析数据")
    
    success_count = 0
    error_count = 0
    
    # 获取活跃用户（有行为数据的用户）
    active_users = User.objects.filter(
        Q(behaviors__isnull=False) | 
        Q(position_applications__isnull=False) | 
        Q(exam_registrations__isnull=False)
    ).distinct()
    
    for user in active_users:
        try:
            # 活跃度统计
            login_count = 0  # 这里需要根据实际登录日志计算
            last_login_date = user.last_login.date() if user.last_login else None
            active_days = 0  # 这里需要根据实际活跃日志计算
            
            # 浏览行为
            position_view_count = UserBehavior.objects.filter(
                user=user, behavior_type='view'
            ).count()
            exam_view_count = 0  # 如果有考试浏览记录的话
            
            # 申请行为
            position_application_count = user.position_applications.count()
            exam_registration_count = user.exam_registrations.count()
            position_favorite_count = user.favorite_positions.count()
            
            # 推荐互动
            recommendation_received_count = user.recommendations.count()
            recommendation_clicked_count = user.recommendations.filter(status='clicked').count()
            recommendation_applied_count = user.recommendations.filter(status='applied').count()
            
            # 成功率
            approved_applications = user.position_applications.filter(status='approved').count()
            application_success_rate = round(
                approved_applications / position_application_count * 100, 2
            ) if position_application_count > 0 else 0
            
            # 创建或更新分析记录
            analytics, created = UserAnalytics.objects.update_or_create(
                user=user,
                defaults={
                    'login_count': login_count,
                    'last_login_date': last_login_date,
                    'active_days': active_days,
                    'position_view_count': position_view_count,
                    'exam_view_count': exam_view_count,
                    'position_application_count': position_application_count,
                    'exam_registration_count': exam_registration_count,
                    'position_favorite_count': position_favorite_count,
                    'recommendation_received_count': recommendation_received_count,
                    'recommendation_clicked_count': recommendation_clicked_count,
                    'recommendation_applied_count': recommendation_applied_count,
                    'application_success_rate': application_success_rate,
                }
            )
            
            success_count += 1
            
        except Exception as e:
            logger.error(f"生成用户 {user.id} 分析数据失败: {e}")
            error_count += 1
    
    logger.info(f"用户分析数据生成完成，成功: {success_count}，失败: {error_count}")
    return {'success': success_count, 'error': error_count}


@shared_task
def cleanup_old_analytics():
    """清理旧的分析数据"""
    logger.info("开始清理旧的分析数据")
    
    # 清理超过90天的系统统计
    cutoff_date = timezone.now().date() - timedelta(days=90)
    
    deleted_counts = {}
    
    # 清理系统统计
    deleted_count = SystemStatistics.objects.filter(stat_date__lt=cutoff_date).count()
    SystemStatistics.objects.filter(stat_date__lt=cutoff_date).delete()
    deleted_counts['system_stats'] = deleted_count
    
    # 清理推荐分析（保留30天）
    rec_cutoff_date = timezone.now().date() - timedelta(days=30)
    deleted_count = RecommendationAnalytics.objects.filter(date__lt=rec_cutoff_date).count()
    RecommendationAnalytics.objects.filter(date__lt=rec_cutoff_date).delete()
    deleted_counts['recommendation_analytics'] = deleted_count
    
    logger.info(f"旧分析数据清理完成: {deleted_counts}")
    return deleted_counts
