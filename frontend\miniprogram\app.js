// app.js
import Toast from '@vant/weapp/toast/toast'
import Dialog from '@vant/weapp/dialog/dialog'
import Notify from '@vant/weapp/notify/notify'

App({
  onLaunch() {
    console.log('小程序启动')
    
    // 检查更新
    this.checkUpdate()
    
    // 初始化用户信息
    this.initUserInfo()
    
    // 设置全局错误处理
    this.setErrorHandler()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(error) {
    console.error('小程序错误:', error)
    Toast.fail('系统错误，请稍后重试')
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(() => {
            Dialog.confirm({
              title: '更新提示',
              message: '新版本已经准备好，是否重启应用？'
            }).then(() => {
              updateManager.applyUpdate()
            })
          })
          
          updateManager.onUpdateFailed(() => {
            Toast.fail('新版本下载失败')
          })
        }
      })
    }
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')
    
    if (userInfo && token) {
      this.globalData.userInfo = userInfo
      this.globalData.token = token
      this.globalData.isLogin = true
    } else {
      this.globalData.isLogin = false
    }
  },

  // 设置全局错误处理
  setErrorHandler() {
    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      console.error('未处理的Promise拒绝:', res)
    })
  },

  // 全局数据
  globalData: {
    userInfo: null,
    token: null,
    isLogin: false,
    baseUrl: 'https://your-api-domain.com/api/v1',
    version: '1.0.0'
  },

  // 工具方法
  utils: {
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hour = String(d.getHours()).padStart(2, '0')
      const minute = String(d.getMinutes()).padStart(2, '0')
      const second = String(d.getSeconds()).padStart(2, '0')
      
      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hour)
        .replace('mm', minute)
        .replace('ss', second)
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 节流函数
    throttle(func, limit) {
      let inThrottle
      return function() {
        const args = arguments
        const context = this
        if (!inThrottle) {
          func.apply(context, args)
          inThrottle = true
          setTimeout(() => inThrottle = false, limit)
        }
      }
    }
  }
})
