"""
推荐系统模型
"""
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.users.models import User
from apps.positions.models import Position


class UserBehavior(models.Model):
    """用户行为记录"""
    
    BEHAVIOR_TYPE_CHOICES = [
        ('view', '浏览'),
        ('favorite', '收藏'),
        ('apply', '申请'),
        ('share', '分享'),
        ('search', '搜索'),
        ('click', '点击'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', related_name='behaviors')
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位', related_name='user_behaviors')
    
    # 行为信息
    behavior_type = models.CharField('行为类型', max_length=20, choices=BEHAVIOR_TYPE_CHOICES)
    behavior_score = models.FloatField('行为分数', default=1.0, 
                                      validators=[MinValueValidator(0), MaxValueValidator(10)])
    
    # 上下文信息
    session_id = models.CharField('会话ID', max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField('IP地址', blank=True, null=True)
    user_agent = models.TextField('用户代理', blank=True, null=True)
    referrer = models.URLField('来源页面', blank=True, null=True)
    
    # 额外数据
    extra_data = models.JSONField('额外数据', blank=True, null=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    
    class Meta:
        db_table = 'recommendations_user_behavior'
        verbose_name = '用户行为'
        verbose_name_plural = '用户行为'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'behavior_type', 'created_at']),
            models.Index(fields=['position', 'behavior_type', 'created_at']),
            models.Index(fields=['behavior_type', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.display_name} {self.get_behavior_type_display()} {self.position.title}"


class UserPreference(models.Model):
    """用户偏好设置"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户', related_name='preference')
    
    # 地理偏好
    preferred_provinces = models.JSONField('偏好省份', blank=True, null=True)
    preferred_cities = models.JSONField('偏好城市', blank=True, null=True)
    
    # 岗位偏好
    preferred_categories = models.JSONField('偏好岗位分类', blank=True, null=True)
    preferred_departments = models.JSONField('偏好部门', blank=True, null=True)
    
    # 薪资偏好
    min_salary = models.DecimalField('最低期望薪资', max_digits=10, decimal_places=2, blank=True, null=True)
    max_salary = models.DecimalField('最高期望薪资', max_digits=10, decimal_places=2, blank=True, null=True)
    
    # 工作类型偏好
    preferred_work_types = models.JSONField('偏好工作类型', blank=True, null=True)
    
    # 推荐设置
    enable_recommendations = models.BooleanField('启用推荐', default=True)
    recommendation_frequency = models.CharField('推荐频率', max_length=20, default='daily',
                                               choices=[
                                                   ('realtime', '实时'),
                                                   ('daily', '每日'),
                                                   ('weekly', '每周'),
                                                   ('monthly', '每月'),
                                               ])
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'recommendations_user_preference'
        verbose_name = '用户偏好'
        verbose_name_plural = '用户偏好'
    
    def __str__(self):
        return f"{self.user.display_name}的偏好设置"


class RecommendationRecord(models.Model):
    """推荐记录"""
    
    ALGORITHM_TYPE_CHOICES = [
        ('collaborative_filtering', '协同过滤'),
        ('content_based', '基于内容'),
        ('hybrid', '混合推荐'),
        ('popularity', '热门推荐'),
        ('random', '随机推荐'),
    ]
    
    STATUS_CHOICES = [
        ('active', '有效'),
        ('clicked', '已点击'),
        ('applied', '已申请'),
        ('expired', '已过期'),
        ('dismissed', '已忽略'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', related_name='recommendations')
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='推荐岗位', related_name='recommendations')
    
    # 推荐信息
    algorithm_type = models.CharField('推荐算法', max_length=50, choices=ALGORITHM_TYPE_CHOICES)
    recommendation_score = models.FloatField('推荐分数', 
                                           validators=[MinValueValidator(0), MaxValueValidator(1)])
    confidence_score = models.FloatField('置信度', blank=True, null=True,
                                        validators=[MinValueValidator(0), MaxValueValidator(1)])
    
    # 推荐理由
    reason = models.TextField('推荐理由', blank=True, null=True)
    feature_weights = models.JSONField('特征权重', blank=True, null=True)
    
    # 状态
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='active')
    
    # 用户反馈
    user_rating = models.IntegerField('用户评分', blank=True, null=True,
                                     validators=[MinValueValidator(1), MaxValueValidator(5)])
    user_feedback = models.TextField('用户反馈', blank=True, null=True)
    
    # 推荐批次
    batch_id = models.CharField('推荐批次ID', max_length=100, blank=True, null=True)
    
    # 时间戳
    recommended_at = models.DateTimeField('推荐时间', default=timezone.now)
    clicked_at = models.DateTimeField('点击时间', blank=True, null=True)
    expires_at = models.DateTimeField('过期时间', blank=True, null=True)
    
    class Meta:
        db_table = 'recommendations_record'
        verbose_name = '推荐记录'
        verbose_name_plural = '推荐记录'
        unique_together = ['user', 'position', 'batch_id']
        ordering = ['-recommended_at']
        indexes = [
            models.Index(fields=['user', 'status', 'recommended_at']),
            models.Index(fields=['position', 'recommended_at']),
            models.Index(fields=['algorithm_type', 'recommended_at']),
            models.Index(fields=['batch_id']),
        ]
    
    def __str__(self):
        return f"推荐 {self.position.title} 给 {self.user.display_name}"


class UserSimilarity(models.Model):
    """用户相似度"""
    
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户1', related_name='similarities_as_user1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户2', related_name='similarities_as_user2')
    
    # 相似度信息
    similarity_score = models.FloatField('相似度分数',
                                        validators=[MinValueValidator(0), MaxValueValidator(1)])
    algorithm_type = models.CharField('算法类型', max_length=50, default='cosine')
    
    # 计算信息
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    data_version = models.CharField('数据版本', max_length=50, blank=True, null=True)
    
    class Meta:
        db_table = 'recommendations_user_similarity'
        verbose_name = '用户相似度'
        verbose_name_plural = '用户相似度'
        unique_together = ['user1', 'user2']
        ordering = ['-similarity_score']
        indexes = [
            models.Index(fields=['user1', 'similarity_score']),
            models.Index(fields=['user2', 'similarity_score']),
        ]
    
    def __str__(self):
        return f"{self.user1.display_name} 与 {self.user2.display_name} 相似度: {self.similarity_score:.3f}"


class PositionSimilarity(models.Model):
    """岗位相似度"""
    
    position1 = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位1', 
                                 related_name='similarities_as_position1')
    position2 = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name='岗位2',
                                 related_name='similarities_as_position2')
    
    # 相似度信息
    similarity_score = models.FloatField('相似度分数',
                                        validators=[MinValueValidator(0), MaxValueValidator(1)])
    algorithm_type = models.CharField('算法类型', max_length=50, default='content_based')
    
    # 特征相似度详情
    feature_similarities = models.JSONField('特征相似度', blank=True, null=True)
    
    # 计算信息
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    data_version = models.CharField('数据版本', max_length=50, blank=True, null=True)
    
    class Meta:
        db_table = 'recommendations_position_similarity'
        verbose_name = '岗位相似度'
        verbose_name_plural = '岗位相似度'
        unique_together = ['position1', 'position2']
        ordering = ['-similarity_score']
        indexes = [
            models.Index(fields=['position1', 'similarity_score']),
            models.Index(fields=['position2', 'similarity_score']),
        ]
    
    def __str__(self):
        return f"{self.position1.title} 与 {self.position2.title} 相似度: {self.similarity_score:.3f}"


class RecommendationFeedback(models.Model):
    """推荐反馈"""
    
    FEEDBACK_TYPE_CHOICES = [
        ('like', '喜欢'),
        ('dislike', '不喜欢'),
        ('not_interested', '不感兴趣'),
        ('irrelevant', '不相关'),
        ('applied', '已申请'),
    ]
    
    recommendation = models.ForeignKey(RecommendationRecord, on_delete=models.CASCADE, 
                                     verbose_name='推荐记录', related_name='feedbacks')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', related_name='recommendation_feedbacks')
    
    # 反馈信息
    feedback_type = models.CharField('反馈类型', max_length=20, choices=FEEDBACK_TYPE_CHOICES)
    rating = models.IntegerField('评分', blank=True, null=True,
                                validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField('评论', blank=True, null=True)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    
    class Meta:
        db_table = 'recommendations_feedback'
        verbose_name = '推荐反馈'
        verbose_name_plural = '推荐反馈'
        unique_together = ['recommendation', 'user']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.display_name} 对推荐的反馈: {self.get_feedback_type_display()}"
