"""
考试管理序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from apps.users.serializers import UserSerializer
from .models import Exam, ExamRegistration, AdmissionTicket, Score


class ExamSerializer(serializers.ModelSerializer):
    """考试基础序列化器"""
    
    created_by = UserSerializer(read_only=True)
    is_registration_open = serializers.ReadOnlyField()
    is_ongoing = serializers.ReadOnlyField()
    
    class Meta:
        model = Exam
        fields = [
            'id', 'name', 'description', 'exam_type', 'start_date', 'end_date',
            'registration_start', 'registration_end', 'exam_location', 'requirements',
            'exam_subjects', 'initial_score_weight', 'assessment_score_weight',
            'initial_pass_score', 'assessment_pass_score', 'status', 'is_published',
            'registration_count', 'participant_count', 'created_by',
            'is_registration_open', 'is_ongoing', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'registration_count', 'participant_count', 'created_by',
            'created_at', 'updated_at'
        ]
    
    def validate(self, attrs):
        """验证考试时间"""
        start_date = attrs.get('start_date')
        end_date = attrs.get('end_date')
        registration_start = attrs.get('registration_start')
        registration_end = attrs.get('registration_end')
        
        # 验证考试时间
        if start_date and end_date and start_date >= end_date:
            raise serializers.ValidationError("考试结束时间必须晚于开始时间")
        
        # 验证报名时间
        if registration_start and registration_end:
            if registration_start >= registration_end:
                raise serializers.ValidationError("报名结束时间必须晚于开始时间")
            
            if start_date and registration_end > start_date:
                raise serializers.ValidationError("报名结束时间不能晚于考试开始时间")
        
        return attrs


class ExamCreateSerializer(serializers.ModelSerializer):
    """考试创建序列化器"""
    
    class Meta:
        model = Exam
        fields = [
            'name', 'description', 'exam_type', 'start_date', 'end_date',
            'registration_start', 'registration_end', 'exam_location', 'requirements',
            'exam_subjects', 'initial_score_weight', 'assessment_score_weight',
            'initial_pass_score', 'assessment_pass_score', 'status', 'is_published'
        ]
    
    def validate(self, attrs):
        """验证考试数据"""
        # 复用基础验证
        return ExamSerializer().validate(attrs)
    
    def create(self, validated_data):
        """创建考试"""
        user = self.context['request'].user
        validated_data['created_by'] = user
        return super().create(validated_data)


class ExamListSerializer(serializers.ModelSerializer):
    """考试列表序列化器"""
    
    is_registration_open = serializers.ReadOnlyField()
    is_ongoing = serializers.ReadOnlyField()
    
    class Meta:
        model = Exam
        fields = [
            'id', 'name', 'exam_type', 'start_date', 'end_date',
            'registration_start', 'registration_end', 'status', 'is_published',
            'registration_count', 'participant_count', 'is_registration_open',
            'is_ongoing', 'created_at'
        ]


class ExamRegistrationSerializer(serializers.ModelSerializer):
    """考试报名序列化器"""
    
    exam = ExamListSerializer(read_only=True)
    candidate = UserSerializer(read_only=True)
    reviewed_by = UserSerializer(read_only=True)
    
    class Meta:
        model = ExamRegistration
        fields = [
            'id', 'exam', 'candidate', 'registration_number', 'status',
            'reviewed_by', 'reviewed_at', 'review_notes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'registration_number', 'reviewed_by', 'reviewed_at',
            'created_at', 'updated_at'
        ]


class ExamRegistrationCreateSerializer(serializers.ModelSerializer):
    """考试报名创建序列化器"""
    
    exam_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ExamRegistration
        fields = ['exam_id']
    
    def validate_exam_id(self, value):
        """验证考试ID"""
        try:
            exam = Exam.objects.get(id=value, is_published=True)
        except Exam.DoesNotExist:
            raise serializers.ValidationError("考试不存在或未发布")
        
        # 检查报名时间
        if not exam.is_registration_open:
            raise serializers.ValidationError("当前不在报名时间内")
        
        # 检查是否已报名
        user = self.context['request'].user
        if ExamRegistration.objects.filter(exam=exam, candidate=user).exists():
            raise serializers.ValidationError("您已报名此考试")
        
        return value
    
    def create(self, validated_data):
        """创建报名记录"""
        exam_id = validated_data['exam_id']
        exam = Exam.objects.get(id=exam_id)
        user = self.context['request'].user
        
        # 生成报名号
        import uuid
        registration_number = f"{exam.id}{timezone.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
        
        registration = ExamRegistration.objects.create(
            exam=exam,
            candidate=user,
            registration_number=registration_number
        )
        
        # 更新考试报名人数
        exam.registration_count = exam.registrations.count()
        exam.save(update_fields=['registration_count'])
        
        return registration


class AdmissionTicketSerializer(serializers.ModelSerializer):
    """准考证序列化器"""
    
    registration = ExamRegistrationSerializer(read_only=True)
    generated_by = UserSerializer(read_only=True)
    
    class Meta:
        model = AdmissionTicket
        fields = [
            'id', 'registration', 'ticket_number', 'exam_room', 'seat_number',
            'exam_time', 'exam_address', 'notes', 'generated_by', 'generated_at'
        ]
        read_only_fields = ['id', 'ticket_number', 'generated_by', 'generated_at']


class AdmissionTicketCreateSerializer(serializers.ModelSerializer):
    """准考证创建序列化器"""
    
    registration_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = AdmissionTicket
        fields = [
            'registration_id', 'exam_room', 'seat_number', 'exam_time',
            'exam_address', 'notes'
        ]
    
    def validate_registration_id(self, value):
        """验证报名记录"""
        try:
            registration = ExamRegistration.objects.get(id=value, status='approved')
        except ExamRegistration.DoesNotExist:
            raise serializers.ValidationError("报名记录不存在或未通过审核")
        
        # 检查是否已生成准考证
        if hasattr(registration, 'admission_ticket'):
            raise serializers.ValidationError("该报名记录已生成准考证")
        
        return value
    
    def create(self, validated_data):
        """创建准考证"""
        registration_id = validated_data.pop('registration_id')
        registration = ExamRegistration.objects.get(id=registration_id)
        user = self.context['request'].user
        
        # 生成准考证号
        import uuid
        ticket_number = f"{registration.exam.id}{timezone.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:6].upper()}"
        
        ticket = AdmissionTicket.objects.create(
            registration=registration,
            ticket_number=ticket_number,
            generated_by=user,
            **validated_data
        )
        
        return ticket


class ScoreSerializer(serializers.ModelSerializer):
    """成绩序列化器"""
    
    exam = ExamListSerializer(read_only=True)
    candidate = UserSerializer(read_only=True)
    entered_by = UserSerializer(read_only=True)
    
    class Meta:
        model = Score
        fields = [
            'id', 'exam', 'candidate', 'initial_score', 'assessment_score',
            'total_score', 'rank', 'is_qualified', 'subject_scores',
            'entered_by', 'entered_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'total_score', 'is_qualified', 'entered_by', 'entered_at',
            'created_at', 'updated_at'
        ]


class ScoreCreateSerializer(serializers.ModelSerializer):
    """成绩创建序列化器"""
    
    exam_id = serializers.IntegerField(write_only=True)
    candidate_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = Score
        fields = [
            'exam_id', 'candidate_id', 'initial_score', 'assessment_score',
            'subject_scores'
        ]
    
    def validate(self, attrs):
        """验证成绩数据"""
        exam_id = attrs.get('exam_id')
        candidate_id = attrs.get('candidate_id')
        
        # 验证考试和考生
        try:
            exam = Exam.objects.get(id=exam_id)
            from apps.users.models import User
            candidate = User.objects.get(id=candidate_id)
        except (Exam.DoesNotExist, User.DoesNotExist):
            raise serializers.ValidationError("考试或考生不存在")
        
        # 检查是否已有成绩记录
        if Score.objects.filter(exam=exam, candidate=candidate).exists():
            raise serializers.ValidationError("该考生已有成绩记录")
        
        # 检查考生是否报名并通过审核
        try:
            registration = ExamRegistration.objects.get(
                exam=exam, candidate=candidate, status='approved'
            )
        except ExamRegistration.DoesNotExist:
            raise serializers.ValidationError("考生未报名此考试或报名未通过审核")
        
        return attrs
    
    def create(self, validated_data):
        """创建成绩记录"""
        exam_id = validated_data.pop('exam_id')
        candidate_id = validated_data.pop('candidate_id')
        
        exam = Exam.objects.get(id=exam_id)
        from apps.users.models import User
        candidate = User.objects.get(id=candidate_id)
        user = self.context['request'].user
        
        score = Score.objects.create(
            exam=exam,
            candidate=candidate,
            entered_by=user,
            entered_at=timezone.now(),
            **validated_data
        )
        
        return score


class ScoreUpdateSerializer(serializers.ModelSerializer):
    """成绩更新序列化器"""
    
    class Meta:
        model = Score
        fields = ['initial_score', 'assessment_score', 'subject_scores']
    
    def update(self, instance, validated_data):
        """更新成绩"""
        user = self.context['request'].user
        instance.entered_by = user
        instance.entered_at = timezone.now()
        
        return super().update(instance, validated_data)
