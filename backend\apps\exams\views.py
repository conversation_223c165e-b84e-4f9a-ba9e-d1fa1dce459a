"""
考试管理视图
"""
import logging
from django.utils import timezone
from django.db.models import Q, Count, Avg, Max, Min
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from utils.response import APIResponse
from utils.permissions import IsAdminUser, IsCandidateUser
from utils.pagination import CustomPageNumberPagination
from .models import Exam, ExamRegistration, AdmissionTicket, Score
from .serializers import (
    ExamSerializer, ExamCreateSerializer, ExamListSerializer,
    ExamRegistrationSerializer, ExamRegistrationCreateSerializer,
    AdmissionTicketSerializer, AdmissionTicketCreateSerializer,
    ScoreSerializer, ScoreCreateSerializer, ScoreUpdateSerializer
)

logger = logging.getLogger(__name__)


class ExamViewSet(ModelViewSet):
    """考试管理"""
    
    queryset = Exam.objects.all()
    serializer_class = ExamSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['exam_type', 'status', 'is_published']
    search_fields = ['name', 'description']
    ordering_fields = ['start_date', 'created_at', 'registration_count']
    ordering = ['-start_date']
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return ExamCreateSerializer
        elif self.action == 'list':
            return ExamListSerializer
        return ExamSerializer
    
    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()
        
        # 非管理员只能看到已发布的考试
        if not (self.request.user.is_authenticated and self.request.user.is_staff):
            queryset = queryset.filter(is_published=True)
        
        return queryset.select_related('created_by')
    
    def create(self, request, *args, **kwargs):
        """创建考试"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            exam = serializer.save()
            return APIResponse.success(
                ExamSerializer(exam).data,
                message="考试创建成功"
            )
        return APIResponse.error("创建失败", data=serializer.errors)
    
    def update(self, request, *args, **kwargs):
        """更新考试"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                serializer.data,
                message="考试信息更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)
    
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取考试统计信息"""
        exam = self.get_object()
        
        # 报名统计
        registrations = exam.registrations.all()
        registration_stats = {
            'total': registrations.count(),
            'pending': registrations.filter(status='pending').count(),
            'approved': registrations.filter(status='approved').count(),
            'rejected': registrations.filter(status='rejected').count(),
        }
        
        # 成绩统计
        scores = exam.scores.all()
        score_stats = {
            'total': scores.count(),
            'qualified': scores.filter(is_qualified=True).count(),
            'avg_total_score': scores.aggregate(avg=Avg('total_score'))['avg'],
            'max_total_score': scores.aggregate(max=Max('total_score'))['max'],
            'min_total_score': scores.aggregate(min=Min('total_score'))['min'],
        }
        
        # 分数段分布
        score_ranges = [
            (90, 100, '优秀'),
            (80, 89, '良好'),
            (70, 79, '中等'),
            (60, 69, '及格'),
            (0, 59, '不及格'),
        ]
        
        score_distribution = []
        for min_score, max_score, label in score_ranges:
            count = scores.filter(
                total_score__gte=min_score,
                total_score__lte=max_score
            ).count()
            score_distribution.append({
                'range': f"{min_score}-{max_score}",
                'label': label,
                'count': count
            })
        
        return APIResponse.success({
            'registration_stats': registration_stats,
            'score_stats': score_stats,
            'score_distribution': score_distribution,
        })
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """发布考试"""
        exam = self.get_object()
        exam.is_published = True
        exam.save(update_fields=['is_published'])
        
        return APIResponse.success(message="考试已发布")
    
    @action(detail=True, methods=['post'])
    def unpublish(self, request, pk=None):
        """取消发布考试"""
        exam = self.get_object()
        exam.is_published = False
        exam.save(update_fields=['is_published'])
        
        return APIResponse.success(message="考试已取消发布")


class ExamRegistrationViewSet(ModelViewSet):
    """考试报名管理"""
    
    queryset = ExamRegistration.objects.all()
    serializer_class = ExamRegistrationSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['exam', 'status']
    search_fields = ['registration_number', 'candidate__username', 'candidate__real_name']
    ordering_fields = ['created_at']
    ordering = ['-created_at']
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action == 'create':
            permission_classes = [IsCandidateUser]
        elif self.action in ['approve', 'reject']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return ExamRegistrationCreateSerializer
        return ExamRegistrationSerializer
    
    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()
        
        # 考生只能看到自己的报名记录
        if (self.request.user.is_authenticated and 
            self.request.user.user_type == 'candidate'):
            queryset = queryset.filter(candidate=self.request.user)
        
        return queryset.select_related('exam', 'candidate', 'reviewed_by')
    
    def create(self, request, *args, **kwargs):
        """创建报名记录"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            registration = serializer.save()
            return APIResponse.success(
                ExamRegistrationSerializer(registration).data,
                message="报名成功"
            )
        return APIResponse.error("报名失败", data=serializer.errors)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审核通过"""
        registration = self.get_object()
        registration.status = 'approved'
        registration.reviewed_by = request.user
        registration.reviewed_at = timezone.now()
        registration.review_notes = request.data.get('review_notes', '')
        registration.save()
        
        return APIResponse.success(message="审核通过")
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """审核拒绝"""
        registration = self.get_object()
        registration.status = 'rejected'
        registration.reviewed_by = request.user
        registration.reviewed_at = timezone.now()
        registration.review_notes = request.data.get('review_notes', '')
        registration.save()
        
        return APIResponse.success(message="审核拒绝")
    
    @action(detail=False, methods=['get'])
    def my_registrations(self, request):
        """我的报名记录"""
        if request.user.user_type != 'candidate':
            return APIResponse.error("只有考生可以查看报名记录")
        
        registrations = self.get_queryset().filter(candidate=request.user)
        page = self.paginate_queryset(registrations)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(registrations, many=True)
        return APIResponse.success(serializer.data)


class AdmissionTicketViewSet(ModelViewSet):
    """准考证管理"""
    
    queryset = AdmissionTicket.objects.all()
    serializer_class = AdmissionTicketSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = ['ticket_number', 'registration__candidate__username']
    
    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return AdmissionTicketCreateSerializer
        return AdmissionTicketSerializer
    
    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()
        
        # 考生只能看到自己的准考证
        if (self.request.user.is_authenticated and 
            self.request.user.user_type == 'candidate'):
            queryset = queryset.filter(registration__candidate=self.request.user)
        
        return queryset.select_related('registration__exam', 'registration__candidate', 'generated_by')
    
    def create(self, request, *args, **kwargs):
        """创建准考证"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            ticket = serializer.save()
            return APIResponse.success(
                AdmissionTicketSerializer(ticket).data,
                message="准考证生成成功"
            )
        return APIResponse.error("生成失败", data=serializer.errors)
    
    @action(detail=False, methods=['get'])
    def my_tickets(self, request):
        """我的准考证"""
        if request.user.user_type != 'candidate':
            return APIResponse.error("只有考生可以查看准考证")
        
        tickets = self.get_queryset().filter(registration__candidate=request.user)
        serializer = self.get_serializer(tickets, many=True)
        return APIResponse.success(serializer.data)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载准考证PDF"""
        ticket = self.get_object()
        
        # 这里应该生成PDF文件
        # 可以使用reportlab库生成PDF
        try:
            from utils.pdf_generator import generate_admission_ticket_pdf
            pdf_content = generate_admission_ticket_pdf(ticket)
            
            response = Response(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="admission_ticket_{ticket.ticket_number}.pdf"'
            return response
            
        except ImportError:
            return APIResponse.error("PDF生成功能暂未实现")
        except Exception as e:
            logger.error(f"生成准考证PDF失败: {e}")
            return APIResponse.error("PDF生成失败")


class ScoreViewSet(ModelViewSet):
    """成绩管理"""

    queryset = Score.objects.all()
    serializer_class = ScoreSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['exam', 'is_qualified']
    search_fields = ['candidate__username', 'candidate__real_name']
    ordering_fields = ['total_score', 'rank', 'created_at']
    ordering = ['-total_score']

    def get_permissions(self):
        """根据动作设置权限"""
        if self.action in ['create', 'update', 'partial_update', 'destroy', 'batch_import']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return ScoreCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return ScoreUpdateSerializer
        return ScoreSerializer

    def get_queryset(self):
        """根据用户类型过滤数据"""
        queryset = super().get_queryset()

        # 考生只能看到自己的成绩
        if (self.request.user.is_authenticated and
            self.request.user.user_type == 'candidate'):
            queryset = queryset.filter(candidate=self.request.user)

        return queryset.select_related('exam', 'candidate', 'entered_by')

    def create(self, request, *args, **kwargs):
        """创建成绩记录"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            score = serializer.save()
            return APIResponse.success(
                ScoreSerializer(score).data,
                message="成绩录入成功"
            )
        return APIResponse.error("录入失败", data=serializer.errors)

    def update(self, request, *args, **kwargs):
        """更新成绩"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return APIResponse.success(
                ScoreSerializer(instance).data,
                message="成绩更新成功"
            )
        return APIResponse.error("更新失败", data=serializer.errors)

    @action(detail=False, methods=['get'])
    def my_scores(self, request):
        """我的成绩"""
        if request.user.user_type != 'candidate':
            return APIResponse.error("只有考生可以查看成绩")

        scores = self.get_queryset().filter(candidate=request.user)
        page = self.paginate_queryset(scores)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(scores, many=True)
        return APIResponse.success(serializer.data)

    @action(detail=False, methods=['post'])
    def batch_import(self, request):
        """批量导入成绩"""
        if 'file' not in request.FILES:
            return APIResponse.error("请上传Excel文件")

        file = request.FILES['file']

        try:
            import pandas as pd

            # 读取Excel文件
            df = pd.read_excel(file)

            # 验证必需列
            required_columns = ['考试ID', '考生ID', '初评成绩', '考核测评成绩']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return APIResponse.error(f"缺少必需列: {', '.join(missing_columns)}")

            success_count = 0
            error_list = []

            for index, row in df.iterrows():
                try:
                    # 创建成绩记录
                    serializer = ScoreCreateSerializer(data={
                        'exam_id': int(row['考试ID']),
                        'candidate_id': int(row['考生ID']),
                        'initial_score': float(row['初评成绩']),
                        'assessment_score': float(row['考核测评成绩']),
                    }, context={'request': request})

                    if serializer.is_valid():
                        serializer.save()
                        success_count += 1
                    else:
                        error_list.append(f"第{index+2}行: {serializer.errors}")

                except Exception as e:
                    error_list.append(f"第{index+2}行: {str(e)}")

            return APIResponse.success({
                'success_count': success_count,
                'error_count': len(error_list),
                'errors': error_list[:10]  # 只返回前10个错误
            }, message=f"导入完成，成功{success_count}条")

        except Exception as e:
            logger.error(f"批量导入成绩失败: {e}")
            return APIResponse.error("文件处理失败")

    @action(detail=False, methods=['post'])
    def calculate_ranks(self, request):
        """计算排名"""
        exam_id = request.data.get('exam_id')

        if not exam_id:
            return APIResponse.error("请指定考试ID")

        try:
            exam = Exam.objects.get(id=exam_id)
        except Exam.DoesNotExist:
            return APIResponse.error("考试不存在")

        # 获取该考试的所有合格成绩，按总分降序排列
        scores = Score.objects.filter(
            exam=exam,
            is_qualified=True
        ).order_by('-total_score')

        # 更新排名
        for rank, score in enumerate(scores, 1):
            score.rank = rank
            score.save(update_fields=['rank'])

        return APIResponse.success(message=f"已更新{scores.count()}条成绩的排名")

    @action(detail=False, methods=['get'])
    def export(self, request):
        """导出成绩"""
        exam_id = request.query_params.get('exam_id')

        if not exam_id:
            return APIResponse.error("请指定考试ID")

        try:
            exam = Exam.objects.get(id=exam_id)
        except Exam.DoesNotExist:
            return APIResponse.error("考试不存在")

        scores = Score.objects.filter(exam=exam).select_related('candidate')

        try:
            import pandas as pd
            from django.http import HttpResponse

            # 准备数据
            data = []
            for score in scores:
                data.append({
                    '考试名称': exam.name,
                    '考生姓名': score.candidate.real_name or score.candidate.username,
                    '身份证号': score.candidate.id_card or '',
                    '初评成绩': score.initial_score,
                    '考核测评成绩': score.assessment_score,
                    '总成绩': score.total_score,
                    '排名': score.rank,
                    '是否合格': '是' if score.is_qualified else '否',
                    '录入时间': score.entered_at.strftime('%Y-%m-%d %H:%M:%S') if score.entered_at else '',
                })

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 生成Excel文件
            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="scores_{exam.name}_{timezone.now().strftime("%Y%m%d")}.xlsx"'

            df.to_excel(response, index=False, engine='openpyxl')

            return response

        except ImportError:
            return APIResponse.error("Excel导出功能需要安装pandas和openpyxl")
        except Exception as e:
            logger.error(f"导出成绩失败: {e}")
            return APIResponse.error("导出失败")
