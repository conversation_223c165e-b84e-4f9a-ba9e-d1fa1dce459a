"""
用户序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, CandidateProfile, RecruiterProfile


class UserSerializer(serializers.ModelSerializer):
    """用户基础序列化器"""
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'phone', 'nickname', 'avatar', 
            'user_type', 'real_name', 'gender', 'birth_date', 
            'is_active', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'phone', 'nickname', 'password', 
            'password_confirm', 'user_type', 'real_name', 'gender'
        ]
    
    def validate(self, attrs):
        """验证密码确认"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("两次输入的密码不一致")
        return attrs
    
    def validate_username(self, value):
        """验证用户名唯一性"""
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已存在")
        return value
    
    def validate_email(self, value):
        """验证邮箱唯一性"""
        if value and User.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱已被注册")
        return value
    
    def validate_phone(self, value):
        """验证手机号唯一性"""
        if value and User.objects.filter(phone=value).exists():
            raise serializers.ValidationError("手机号已被注册")
        return value
    
    def create(self, validated_data):
        """创建用户"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user



    
    def validate_code(self, value):
        """验证微信授权码"""
        if not value:
            raise serializers.ValidationError("微信授权码不能为空")
        return value


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    
    old_password = serializers.CharField(help_text="原密码")
    new_password = serializers.CharField(validators=[validate_password], help_text="新密码")
    new_password_confirm = serializers.CharField(help_text="确认新密码")
    
    def validate_old_password(self, value):
        """验证原密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("原密码错误")
        return value
    
    def validate(self, attrs):
        """验证新密码确认"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("两次输入的新密码不一致")
        return attrs
    
    def save(self):
        """保存新密码"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class CandidateProfileSerializer(serializers.ModelSerializer):
    """考生信息序列化器"""
    
    user = UserSerializer(read_only=True)
    age = serializers.ReadOnlyField()
    
    class Meta:
        model = CandidateProfile
        fields = [
            'id', 'user', 'education', 'major', 'graduation_school', 'graduation_year',
            'work_experience', 'current_position', 'work_years', 'address',
            'emergency_contact', 'emergency_phone', 'political_status',
            'skills', 'certificates', 'preferred_location', 'preferred_salary_min',
            'preferred_salary_max', 'is_verified', 'verified_at', 'age',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'verified_at', 'created_at', 'updated_at']


class CandidateProfileCreateSerializer(serializers.ModelSerializer):
    """考生信息创建序列化器"""
    
    class Meta:
        model = CandidateProfile
        fields = [
            'education', 'major', 'graduation_school', 'graduation_year',
            'work_experience', 'current_position', 'work_years', 'address',
            'emergency_contact', 'emergency_phone', 'political_status',
            'skills', 'certificates', 'preferred_location', 'preferred_salary_min',
            'preferred_salary_max'
        ]
    
    def create(self, validated_data):
        """创建考生信息"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)


class RecruiterProfileSerializer(serializers.ModelSerializer):
    """招聘单位信息序列化器"""
    
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = RecruiterProfile
        fields = [
            'id', 'user', 'organization_name', 'organization_type', 'organization_code',
            'legal_representative', 'contact_person', 'contact_phone', 'contact_email',
            'office_address', 'description', 'website', 'is_verified', 'verified_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_verified', 'verified_at', 'created_at', 'updated_at']


class RecruiterProfileCreateSerializer(serializers.ModelSerializer):
    """招聘单位信息创建序列化器"""
    
    class Meta:
        model = RecruiterProfile
        fields = [
            'organization_name', 'organization_type', 'organization_code',
            'legal_representative', 'contact_person', 'contact_phone', 'contact_email',
            'office_address', 'description', 'website'
        ]
    
    def create(self, validated_data):
        """创建招聘单位信息"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)


class UserProfileSerializer(serializers.ModelSerializer):
    """用户完整信息序列化器"""
    
    candidate_profile = CandidateProfileSerializer(read_only=True)
    recruiter_profile = RecruiterProfileSerializer(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'phone', 'nickname', 'avatar', 
            'user_type', 'real_name', 'id_card', 'gender', 'birth_date',
            'is_active', 'date_joined', 'last_login',
            'candidate_profile', 'recruiter_profile'
        ]
        read_only_fields = ['id', 'username', 'date_joined', 'last_login']


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器"""
    
    class Meta:
        model = User
        fields = [
            'email', 'phone', 'nickname', 'avatar', 'real_name', 
            'id_card', 'gender', 'birth_date'
        ]
    
    def validate_email(self, value):
        """验证邮箱唯一性"""
        user = self.instance
        if value and User.objects.filter(email=value).exclude(id=user.id).exists():
            raise serializers.ValidationError("邮箱已被其他用户注册")
        return value
    
    def validate_phone(self, value):
        """验证手机号唯一性"""
        user = self.instance
        if value and User.objects.filter(phone=value).exclude(id=user.id).exists():
            raise serializers.ValidationError("手机号已被其他用户注册")
        return value
    
    def validate_id_card(self, value):
        """验证身份证号唯一性"""
        user = self.instance
        if value and User.objects.filter(id_card=value).exclude(id=user.id).exists():
            raise serializers.ValidationError("身份证号已被其他用户注册")
        return value
