import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 样式
import 'normalize.css/normalize.css'
import '@/styles/index.scss'

// 全局组件
import SvgIcon from '@/components/SvgIcon/index.vue'
import '@/icons' // svg icons

// 权限控制
import '@/permission'

// 工具库
import * as utils from '@/utils'

// 全局配置
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册全局组件
app.component('SvgIcon', SvgIcon)

// 全局属性
app.config.globalProperties.$utils = utils

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 这里可以添加错误上报逻辑
}

// 使用插件
app.use(store)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 挂载应用
app.mount('#app')

// 开发环境配置
if (process.env.NODE_ENV === 'development') {
  app.config.performance = true
  
  // 开发工具
  if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
    window.__VUE_DEVTOOLS_GLOBAL_HOOK__.Vue = app
  }
}

export default app
