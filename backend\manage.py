#!/usr/bin/env python
"""
Django的命令行工具，用于管理项目
"""
import os
import sys

if __name__ == '__main__':
    # 设置Django配置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "无法导入Django。请确保已安装Django并且"
            "在PYTHONPATH环境变量中可用。您是否忘记激活虚拟环境？"
        ) from exc
    execute_from_command_line(sys.argv)
