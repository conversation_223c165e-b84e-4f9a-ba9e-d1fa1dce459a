// api/index.js
import { get, post, put, delete as del, upload } from '../utils/request'

// 用户相关API
export const userApi = {
  // 微信登录
  wechatLogin: (data) => post('/users/auth/wechat-login/', data),
  
  // 获取用户信息
  getUserInfo: () => get('/users/users/profile/'),
  
  // 更新用户信息
  updateUserInfo: (data) => put('/users/users/profile/', data),
  
  // 修改密码
  changePassword: (data) => post('/users/users/change_password/', data),
  
  // 上传头像
  uploadAvatar: (filePath) => upload('/users/users/upload_avatar/', filePath, { name: 'avatar' }),
  
  // 获取考生信息
  getCandidateProfile: () => get('/users/candidate-profiles/'),
  
  // 创建考生信息
  createCandidateProfile: (data) => post('/users/candidate-profiles/', data),
  
  // 更新考生信息
  updateCandidateProfile: (id, data) => put(`/users/candidate-profiles/${id}/`, data),
  
  // 获取招聘单位信息
  getRecruiterProfile: () => get('/users/recruiter-profiles/'),
  
  // 创建招聘单位信息
  createRecruiterProfile: (data) => post('/users/recruiter-profiles/', data),
  
  // 更新招聘单位信息
  updateRecruiterProfile: (id, data) => put(`/users/recruiter-profiles/${id}/`, data)
}

// 考试相关API
export const examApi = {
  // 获取考试列表
  getExamList: (params) => get('/exams/exams/', params),
  
  // 获取考试详情
  getExamDetail: (id) => get(`/exams/exams/${id}/`),
  
  // 获取考试统计
  getExamStatistics: (id) => get(`/exams/exams/${id}/statistics/`),
  
  // 报名考试
  registerExam: (data) => post('/exams/registrations/', data),
  
  // 获取我的报名记录
  getMyRegistrations: (params) => get('/exams/registrations/my_registrations/', params),
  
  // 获取准考证
  getAdmissionTickets: (params) => get('/exams/admission-tickets/my_tickets/', params),
  
  // 下载准考证
  downloadAdmissionTicket: (id) => get(`/exams/admission-tickets/${id}/download/`),
  
  // 获取我的成绩
  getMyScores: (params) => get('/exams/scores/my_scores/', params)
}

// 岗位相关API
export const positionApi = {
  // 获取岗位分类
  getPositionCategories: () => get('/positions/categories/tree/'),
  
  // 获取岗位列表
  getPositionList: (params) => get('/positions/positions/', params),
  
  // 获取岗位详情
  getPositionDetail: (id) => get(`/positions/positions/${id}/`),
  
  // 搜索岗位
  searchPositions: (data) => post('/positions/positions/search/', data),
  
  // 申请岗位
  applyPosition: (id, data) => post(`/positions/positions/${id}/apply/`, data),
  
  // 收藏岗位
  favoritePosition: (id) => post(`/positions/positions/${id}/favorite/`),
  
  // 取消收藏
  unfavoritePosition: (id) => post(`/positions/positions/${id}/unfavorite/`),
  
  // 获取岗位统计
  getPositionStatistics: (id) => get(`/positions/positions/${id}/statistics/`),
  
  // 获取我的申请记录
  getMyApplications: (params) => get('/positions/applications/my_applications/', params),
  
  // 获取我的收藏
  getMyFavorites: (params) => get('/positions/favorites/my_favorites/', params)
}

// 推荐相关API
export const recommendationApi = {
  // 生成推荐
  generateRecommendations: (data) => post('/recommendations/recommendations/generate/', data),
  
  // 获取我的推荐
  getMyRecommendations: (params) => get('/recommendations/recommendations/my_recommendations/', params),
  
  // 点击推荐
  clickRecommendation: (id) => post(`/recommendations/recommendations/${id}/click/`),
  
  // 提交推荐反馈
  submitFeedback: (data) => post('/recommendations/feedbacks/', data),
  
  // 记录用户行为
  recordBehavior: (data) => post('/recommendations/behaviors/', data),
  
  // 获取用户偏好
  getUserPreference: () => get('/recommendations/preferences/my_preference/'),
  
  // 设置用户偏好
  setUserPreference: (data) => post('/recommendations/preferences/my_preference/', data),
  
  // 获取推荐分析
  getRecommendationAnalytics: () => get('/recommendations/analytics/')
}

// 通用API
export const commonApi = {
  // 获取地区列表
  getRegions: () => get('/common/regions/'),
  
  // 获取学历选项
  getEducationOptions: () => get('/common/education-options/'),
  
  // 获取工作类型选项
  getWorkTypeOptions: () => get('/common/work-type-options/'),
  
  // 上传文件
  uploadFile: (filePath, options = {}) => upload('/common/upload/', filePath, options),
  
  // 获取系统配置
  getSystemConfig: () => get('/common/config/'),
  
  // 获取公告列表
  getNotices: (params) => get('/common/notices/', params),
  
  // 获取公告详情
  getNoticeDetail: (id) => get(`/common/notices/${id}/`),
  
  // 意见反馈
  submitFeedback: (data) => post('/common/feedback/', data)
}

// 导出所有API
export default {
  user: userApi,
  exam: examApi,
  position: positionApi,
  recommendation: recommendationApi,
  common: commonApi
}
