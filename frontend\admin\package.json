{"name": "qiannan-exam-admin", "version": "1.0.0", "description": "黔南州考试岗位推荐系统管理端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build:prod": "vue-cli-service build --mode production", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js", "new": "plop"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "vuex": "^4.1.0", "element-plus": "^2.3.0", "axios": "^1.4.0", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.0", "vue-echarts": "^6.6.0", "dayjs": "^1.11.0", "js-cookie": "^3.0.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "screenfull": "^6.0.0", "sortablejs": "^1.15.0", "normalize.css": "^8.0.1"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-router": "^5.0.0", "@vue/cli-plugin-vuex": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/eslint-config-standard": "^8.0.0", "eslint": "^8.0.0", "eslint-plugin-import": "^2.25.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.0.0", "sass": "^1.62.0", "sass-loader": "^13.2.0", "plop": "^3.1.0", "svg-sprite-loader": "^6.0.0", "webpack": "^5.80.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["vue3", "element-plus", "admin", "exam-system", "recommendation"], "author": "<PERSON>an<PERSON>an Exam System Team", "license": "MIT"}