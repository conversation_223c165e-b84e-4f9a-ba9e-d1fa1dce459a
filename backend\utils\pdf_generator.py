"""
PDF生成工具
"""
import io
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from django.conf import settings


def register_chinese_fonts():
    """注册中文字体"""
    try:
        # 尝试注册系统中文字体
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',  # macOS
            'C:/Windows/Fonts/msyh.ttc',  # Windows
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Chinese', font_path))
                return True
        
        # 如果没有找到系统字体，使用默认字体
        return False
        
    except Exception:
        return False


def generate_admission_ticket_pdf(admission_ticket):
    """
    生成准考证PDF
    
    Args:
        admission_ticket: 准考证对象
        
    Returns:
        bytes: PDF文件内容
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    
    # 注册中文字体
    has_chinese_font = register_chinese_fonts()
    font_name = 'Chinese' if has_chinese_font else 'Helvetica'
    
    # 创建样式
    styles = getSampleStyleSheet()
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        fontName=font_name
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        fontName=font_name
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        fontName=font_name
    )
    
    # 构建PDF内容
    story = []
    
    # 标题
    title = Paragraph("黔南州政策性考试准考证", title_style)
    story.append(title)
    story.append(Spacer(1, 20))
    
    # 考试信息
    exam = admission_ticket.registration.exam
    candidate = admission_ticket.registration.candidate
    
    # 基本信息表格
    data = [
        ['准考证号:', admission_ticket.ticket_number],
        ['考生姓名:', candidate.real_name or candidate.username],
        ['身份证号:', candidate.id_card or ''],
        ['考试名称:', exam.name],
        ['考试时间:', admission_ticket.exam_time.strftime('%Y年%m月%d日 %H:%M') if admission_ticket.exam_time else exam.start_date.strftime('%Y年%m月%d日 %H:%M')],
        ['考试地点:', admission_ticket.exam_address or exam.exam_location or ''],
        ['考场:', admission_ticket.exam_room or ''],
        ['座位号:', admission_ticket.seat_number or ''],
    ]
    
    table = Table(data, colWidths=[2*inch, 4*inch])
    table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
    ]))
    
    story.append(table)
    story.append(Spacer(1, 30))
    
    # 注意事项
    if admission_ticket.notes:
        notes_title = Paragraph("注意事项", heading_style)
        story.append(notes_title)
        
        notes_content = Paragraph(admission_ticket.notes, normal_style)
        story.append(notes_content)
        story.append(Spacer(1, 20))
    
    # 默认注意事项
    default_notes = [
        "1. 考生须持本人有效身份证件和准考证参加考试。",
        "2. 考试开始30分钟后，迟到考生不得入场。",
        "3. 考试期间不得携带手机等电子设备。",
        "4. 考生应诚信考试，严禁作弊。",
        "5. 考试结束后，考生应立即停止答题。",
    ]
    
    notes_title = Paragraph("考试须知", heading_style)
    story.append(notes_title)
    
    for note in default_notes:
        note_para = Paragraph(note, normal_style)
        story.append(note_para)
    
    story.append(Spacer(1, 30))
    
    # 页脚信息
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_CENTER,
        fontName=font_name
    )
    
    footer = Paragraph(
        f"生成时间: {admission_ticket.generated_at.strftime('%Y年%m月%d日 %H:%M:%S')}<br/>黔南州考试管理系统",
        footer_style
    )
    story.append(footer)
    
    # 生成PDF
    doc.build(story)
    
    pdf_content = buffer.getvalue()
    buffer.close()
    
    return pdf_content


def generate_score_report_pdf(score):
    """
    生成成绩单PDF
    
    Args:
        score: 成绩对象
        
    Returns:
        bytes: PDF文件内容
    """
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    
    # 注册中文字体
    has_chinese_font = register_chinese_fonts()
    font_name = 'Chinese' if has_chinese_font else 'Helvetica'
    
    # 创建样式
    styles = getSampleStyleSheet()
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        fontName=font_name
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        fontName=font_name
    )
    
    # 构建PDF内容
    story = []
    
    # 标题
    title = Paragraph("考试成绩单", title_style)
    story.append(title)
    story.append(Spacer(1, 20))
    
    # 成绩信息表格
    data = [
        ['考生姓名:', score.candidate.real_name or score.candidate.username],
        ['身份证号:', score.candidate.id_card or ''],
        ['考试名称:', score.exam.name],
        ['考试时间:', score.exam.start_date.strftime('%Y年%m月%d日')],
        ['初评成绩:', f"{score.initial_score}分" if score.initial_score else ''],
        ['考核测评成绩:', f"{score.assessment_score}分" if score.assessment_score else ''],
        ['总成绩:', f"{score.total_score}分" if score.total_score else ''],
        ['排名:', f"第{score.rank}名" if score.rank else ''],
        ['考试结果:', '合格' if score.is_qualified else '不合格'],
    ]
    
    table = Table(data, colWidths=[2*inch, 4*inch])
    table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), font_name),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
    ]))
    
    story.append(table)
    story.append(Spacer(1, 30))
    
    # 成绩说明
    explanation = [
        f"总成绩计算方式: 初评成绩 × {score.exam.initial_score_weight} + 考核测评成绩 × {score.exam.assessment_score_weight}",
        f"初评合格分数线: {score.exam.initial_pass_score}分",
        f"考核测评合格分数线: {score.exam.assessment_pass_score}分",
        "注: 两项成绩均需达到合格分数线方可获得总成绩。",
    ]
    
    for exp in explanation:
        exp_para = Paragraph(exp, normal_style)
        story.append(exp_para)
    
    story.append(Spacer(1, 30))
    
    # 页脚信息
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_CENTER,
        fontName=font_name
    )
    
    footer = Paragraph(
        f"查询时间: {score.created_at.strftime('%Y年%m月%d日 %H:%M:%S')}<br/>黔南州考试管理系统",
        footer_style
    )
    story.append(footer)
    
    # 生成PDF
    doc.build(story)
    
    pdf_content = buffer.getvalue()
    buffer.close()
    
    return pdf_content
