// utils/request.js
import Toast from '@vant/weapp/toast/toast'

const app = getApp()

// 请求基础配置
const config = {
  baseUrl: 'https://your-api-domain.com/api/v1',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

// 请求拦截器
function requestInterceptor(options) {
  // 显示加载提示
  if (options.showLoading !== false) {
    wx.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    })
  }

  // 添加token
  const token = wx.getStorageSync('token')
  if (token) {
    options.header = {
      ...options.header,
      'Authorization': `Bearer ${token}`
    }
  }

  // 处理URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseUrl + options.url
  }

  return options
}

// 响应拦截器
function responseInterceptor(response, options) {
  // 隐藏加载提示
  if (options.showLoading !== false) {
    wx.hideLoading()
  }

  const { statusCode, data } = response

  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务状态码检查
    if (data.success === false) {
      // 业务错误
      if (options.showError !== false) {
        Toast.fail(data.message || '请求失败')
      }
      return Promise.reject(data)
    }
    
    // 成功响应
    return data
  } else if (statusCode === 401) {
    // 未授权，清除登录信息
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    app.globalData.isLogin = false
    app.globalData.userInfo = null
    app.globalData.token = null
    
    Toast.fail('登录已过期，请重新登录')
    
    // 跳转到登录页
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/login/login'
      })
    }, 1500)
    
    return Promise.reject({ message: '登录已过期' })
  } else if (statusCode === 403) {
    Toast.fail('权限不足')
    return Promise.reject({ message: '权限不足' })
  } else if (statusCode >= 500) {
    Toast.fail('服务器错误')
    return Promise.reject({ message: '服务器错误' })
  } else {
    Toast.fail('网络错误')
    return Promise.reject({ message: '网络错误' })
  }
}

// 错误处理
function errorHandler(error, options) {
  // 隐藏加载提示
  if (options.showLoading !== false) {
    wx.hideLoading()
  }

  console.error('请求错误:', error)

  if (options.showError !== false) {
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        Toast.fail('请求超时')
      } else if (error.errMsg.includes('fail')) {
        Toast.fail('网络连接失败')
      } else {
        Toast.fail('请求失败')
      }
    } else {
      Toast.fail(error.message || '请求失败')
    }
  }

  return Promise.reject(error)
}

// 主请求函数
function request(options) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const requestOptions = requestInterceptor({
      ...config,
      ...options
    })

    wx.request({
      ...requestOptions,
      success: (response) => {
        responseInterceptor(response, options)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {
        errorHandler(error, options).catch(reject)
      }
    })
  })
}

// GET请求
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 文件上传
function upload(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }

    // 添加token
    const token = wx.getStorageSync('token')
    const header = {
      ...options.header
    }
    if (token) {
      header['Authorization'] = `Bearer ${token}`
    }

    // 处理URL
    if (!url.startsWith('http')) {
      url = config.baseUrl + url
    }

    wx.uploadFile({
      url,
      filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header,
      success: (response) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        try {
          const data = JSON.parse(response.data)
          if (data.success === false) {
            if (options.showError !== false) {
              Toast.fail(data.message || '上传失败')
            }
            reject(data)
          } else {
            resolve(data)
          }
        } catch (e) {
          if (options.showError !== false) {
            Toast.fail('上传失败')
          }
          reject({ message: '上传失败' })
        }
      },
      fail: (error) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        if (options.showError !== false) {
          Toast.fail('上传失败')
        }
        reject(error)
      }
    })
  })
}

// 下载文件
function download(url, options = {}) {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '下载中...',
        mask: true
      })
    }

    // 处理URL
    if (!url.startsWith('http')) {
      url = config.baseUrl + url
    }

    wx.downloadFile({
      url,
      success: (response) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        if (response.statusCode === 200) {
          resolve(response)
        } else {
          if (options.showError !== false) {
            Toast.fail('下载失败')
          }
          reject({ message: '下载失败' })
        }
      },
      fail: (error) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        if (options.showError !== false) {
          Toast.fail('下载失败')
        }
        reject(error)
      }
    })
  })
}

export {
  request,
  get,
  post,
  put,
  del as delete,
  upload,
  download
}
