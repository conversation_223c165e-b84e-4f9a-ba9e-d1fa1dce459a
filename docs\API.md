# API 接口文档

## 概述

黔南州考试岗位推荐系统提供完整的RESTful API接口，支持用户认证、考试管理、岗位管理、推荐算法等核心功能。

## 基础信息

- **Base URL**: `https://api.qiannan-exam.gov.cn/api/v1`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

### 获取Token

```http
POST /users/auth/login/
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>",
            "user_type": "candidate"
        }
    }
}
```

### 使用Token

在请求头中添加Authorization字段：
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 用户管理 API

### 用户注册

```http
POST /users/users/register/
Content-Type: application/json

{
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "real_name": "张三",
    "phone": "13800138000",
    "user_type": "candidate"
}
```

### 获取用户信息

```http
GET /users/users/profile/
Authorization: Bearer {token}
```

### 更新用户信息

```http
PUT /users/users/profile/
Authorization: Bearer {token}
Content-Type: application/json

{
    "real_name": "李四",
    "phone": "13900139000",
    "email": "<EMAIL>"
}
```

## 考试管理 API

### 获取考试列表

```http
GET /exams/exams/
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `status`: 考试状态 (upcoming/ongoing/completed)
- `search`: 搜索关键词

**响应示例**:
```json
{
    "success": true,
    "data": {
        "count": 50,
        "next": "http://api.example.com/exams/exams/?page=2",
        "previous": null,
        "results": [
            {
                "id": 1,
                "name": "2024年黔南州公务员考试",
                "description": "面向全州招录公务员",
                "exam_date": "2024-03-15",
                "registration_start": "2024-01-01",
                "registration_end": "2024-02-15",
                "status": "upcoming",
                "exam_location": "黔南州政府大楼",
                "total_positions": 100,
                "registration_count": 1500
            }
        ]
    }
}
```

### 获取考试详情

```http
GET /exams/exams/{id}/
```

### 报名考试

```http
POST /exams/registrations/
Authorization: Bearer {token}
Content-Type: application/json

{
    "exam": 1,
    "position_preferences": [1, 2, 3]
}
```

### 获取我的报名记录

```http
GET /exams/registrations/my_registrations/
Authorization: Bearer {token}
```

## 岗位管理 API

### 获取岗位列表

```http
GET /positions/positions/
```

**查询参数**:
- `page`: 页码
- `page_size`: 每页数量
- `category`: 岗位分类ID
- `location`: 工作地点
- `education`: 学历要求
- `work_type`: 工作类型
- `search`: 搜索关键词
- `ordering`: 排序字段 (-created_at, salary_min, -application_count)

### 获取岗位详情

```http
GET /positions/positions/{id}/
```

### 申请岗位

```http
POST /positions/positions/{id}/apply/
Authorization: Bearer {token}
Content-Type: application/json

{
    "cover_letter": "我对这个岗位很感兴趣...",
    "attachments": ["resume.pdf"]
}
```

### 收藏岗位

```http
POST /positions/positions/{id}/favorite/
Authorization: Bearer {token}
```

### 取消收藏

```http
DELETE /positions/positions/{id}/favorite/
Authorization: Bearer {token}
```

### 获取我的申请记录

```http
GET /positions/applications/my_applications/
Authorization: Bearer {token}
```

### 获取我的收藏

```http
GET /positions/favorites/my_favorites/
Authorization: Bearer {token}
```

## 推荐系统 API

### 生成推荐

```http
POST /recommendations/recommendations/generate/
Authorization: Bearer {token}
Content-Type: application/json

{
    "algorithm_type": "hybrid",
    "count": 10,
    "filters": {
        "location": "都匀市",
        "education": "bachelor"
    }
}
```

**算法类型**:
- `collaborative_filtering`: 协同过滤
- `content_based`: 基于内容
- `hybrid`: 混合推荐
- `popularity`: 热门推荐

### 获取我的推荐

```http
GET /recommendations/recommendations/my_recommendations/
Authorization: Bearer {token}
```

### 点击推荐

```http
POST /recommendations/recommendations/{id}/click/
Authorization: Bearer {token}
```

### 提交推荐反馈

```http
POST /recommendations/feedbacks/
Authorization: Bearer {token}
Content-Type: application/json

{
    "recommendation": 1,
    "rating": 4,
    "feedback_type": "positive",
    "comment": "推荐很准确"
}
```

## 数据分析 API

### 获取仪表板数据

```http
GET /analytics/dashboard/
Authorization: Bearer {token}
```

### 生成分析报告

```http
POST /analytics/analyze/
Authorization: Bearer {token}
Content-Type: application/json

{
    "analysis_type": "exam_report",
    "filters": {
        "exam_id": 1
    },
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
}
```

### 导出数据

```http
POST /analytics/export/
Authorization: Bearer {token}
Content-Type: application/json

{
    "export_type": "excel",
    "data_type": "exam_analytics",
    "filters": {
        "exam_id": 1
    }
}
```

## 错误处理

### 错误响应格式

```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求参数验证失败",
        "details": {
            "username": ["该字段是必填项"]
        }
    }
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| AUTHENTICATION_REQUIRED | 401 | 需要身份认证 |
| PERMISSION_DENIED | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| METHOD_NOT_ALLOWED | 405 | 请求方法不允许 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 分页

所有列表接口都支持分页，使用以下参数：

- `page`: 页码，从1开始
- `page_size`: 每页数量，默认20，最大100

分页响应格式：
```json
{
    "success": true,
    "data": {
        "count": 100,
        "next": "http://api.example.com/endpoint/?page=3",
        "previous": "http://api.example.com/endpoint/?page=1",
        "results": []
    }
}
```

## 排序

支持排序的接口可以使用`ordering`参数：

- 升序: `ordering=field_name`
- 降序: `ordering=-field_name`
- 多字段: `ordering=field1,-field2`

## 过滤

支持过滤的接口可以使用相应的查询参数进行数据过滤。

## 搜索

支持搜索的接口可以使用`search`参数进行全文搜索。

## 限流

API接口实施了请求频率限制：

- 普通接口: 每分钟100次请求
- 登录接口: 每分钟5次请求
- 文件上传: 每分钟10次请求

## 版本控制

API版本通过URL路径指定，当前版本为v1。未来版本更新时会保持向后兼容。

## 联系支持

如有API使用问题，请联系：
- 邮箱: <EMAIL>
- 技术文档: https://docs.qiannan-exam.gov.cn
