"""
数据分析模型
"""
from django.db import models
from django.utils import timezone
from apps.users.models import User
from apps.exams.models import Exam
from apps.positions.models import Position


class SystemStatistics(models.Model):
    """系统统计数据"""
    
    STAT_TYPE_CHOICES = [
        ('daily', '日统计'),
        ('weekly', '周统计'),
        ('monthly', '月统计'),
        ('yearly', '年统计'),
    ]
    
    # 统计信息
    stat_type = models.CharField('统计类型', max_length=20, choices=STAT_TYPE_CHOICES)
    stat_date = models.DateField('统计日期')
    
    # 用户统计
    total_users = models.IntegerField('总用户数', default=0)
    new_users = models.IntegerField('新增用户数', default=0)
    active_users = models.IntegerField('活跃用户数', default=0)
    candidate_users = models.IntegerField('考生用户数', default=0)
    recruiter_users = models.IntegerField('招聘单位用户数', default=0)
    
    # 考试统计
    total_exams = models.IntegerField('总考试数', default=0)
    new_exams = models.IntegerField('新增考试数', default=0)
    ongoing_exams = models.IntegerField('进行中考试数', default=0)
    total_registrations = models.IntegerField('总报名数', default=0)
    new_registrations = models.IntegerField('新增报名数', default=0)
    
    # 岗位统计
    total_positions = models.IntegerField('总岗位数', default=0)
    new_positions = models.IntegerField('新增岗位数', default=0)
    active_positions = models.IntegerField('招聘中岗位数', default=0)
    total_applications = models.IntegerField('总申请数', default=0)
    new_applications = models.IntegerField('新增申请数', default=0)
    
    # 推荐统计
    total_recommendations = models.IntegerField('总推荐数', default=0)
    new_recommendations = models.IntegerField('新增推荐数', default=0)
    recommendation_clicks = models.IntegerField('推荐点击数', default=0)
    recommendation_applications = models.IntegerField('推荐申请数', default=0)
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'analytics_system_statistics'
        verbose_name = '系统统计'
        verbose_name_plural = '系统统计'
        unique_together = ['stat_type', 'stat_date']
        ordering = ['-stat_date']
    
    def __str__(self):
        return f"{self.get_stat_type_display()} - {self.stat_date}"


class ExamAnalytics(models.Model):
    """考试分析数据"""
    
    exam = models.OneToOneField(Exam, on_delete=models.CASCADE, verbose_name='考试', related_name='analytics')
    
    # 报名统计
    registration_count = models.IntegerField('报名人数', default=0)
    approved_registration_count = models.IntegerField('审核通过报名数', default=0)
    participant_count = models.IntegerField('实际参考人数', default=0)
    attendance_rate = models.DecimalField('出考率', max_digits=5, decimal_places=2, default=0)
    
    # 成绩统计
    score_count = models.IntegerField('有成绩人数', default=0)
    qualified_count = models.IntegerField('合格人数', default=0)
    qualification_rate = models.DecimalField('合格率', max_digits=5, decimal_places=2, default=0)
    
    # 成绩分布
    avg_initial_score = models.DecimalField('平均初评成绩', max_digits=5, decimal_places=2, blank=True, null=True)
    avg_assessment_score = models.DecimalField('平均考核测评成绩', max_digits=5, decimal_places=2, blank=True, null=True)
    avg_total_score = models.DecimalField('平均总成绩', max_digits=5, decimal_places=2, blank=True, null=True)
    
    max_total_score = models.DecimalField('最高总成绩', max_digits=5, decimal_places=2, blank=True, null=True)
    min_total_score = models.DecimalField('最低总成绩', max_digits=5, decimal_places=2, blank=True, null=True)
    
    # 分数段分布（JSON格式）
    score_distribution = models.JSONField('分数段分布', blank=True, null=True)
    
    # 地区分布
    region_distribution = models.JSONField('地区分布', blank=True, null=True)
    
    # 学历分布
    education_distribution = models.JSONField('学历分布', blank=True, null=True)
    
    # 年龄分布
    age_distribution = models.JSONField('年龄分布', blank=True, null=True)
    
    # 时间戳
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'analytics_exam_analytics'
        verbose_name = '考试分析'
        verbose_name_plural = '考试分析'
    
    def __str__(self):
        return f"{self.exam.name} - 分析数据"


class PositionAnalytics(models.Model):
    """岗位分析数据"""
    
    position = models.OneToOneField(Position, on_delete=models.CASCADE, verbose_name='岗位', related_name='analytics')
    
    # 浏览统计
    total_views = models.IntegerField('总浏览量', default=0)
    unique_views = models.IntegerField('独立浏览量', default=0)
    daily_views = models.IntegerField('日浏览量', default=0)
    
    # 申请统计
    total_applications = models.IntegerField('总申请数', default=0)
    pending_applications = models.IntegerField('待审核申请数', default=0)
    approved_applications = models.IntegerField('通过申请数', default=0)
    rejected_applications = models.IntegerField('拒绝申请数', default=0)
    
    # 转化率
    view_to_application_rate = models.DecimalField('浏览转申请率', max_digits=5, decimal_places=2, default=0)
    application_approval_rate = models.DecimalField('申请通过率', max_digits=5, decimal_places=2, default=0)
    
    # 收藏统计
    total_favorites = models.IntegerField('总收藏数', default=0)
    
    # 竞争度
    competition_ratio = models.DecimalField('竞争比例', max_digits=8, decimal_places=2, default=0)  # 申请人数/招聘人数
    
    # 申请人分析
    applicant_education_distribution = models.JSONField('申请人学历分布', blank=True, null=True)
    applicant_age_distribution = models.JSONField('申请人年龄分布', blank=True, null=True)
    applicant_region_distribution = models.JSONField('申请人地区分布', blank=True, null=True)
    
    # 推荐统计
    recommendation_count = models.IntegerField('推荐次数', default=0)
    recommendation_click_count = models.IntegerField('推荐点击次数', default=0)
    recommendation_application_count = models.IntegerField('推荐申请次数', default=0)
    
    # 时间戳
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'analytics_position_analytics'
        verbose_name = '岗位分析'
        verbose_name_plural = '岗位分析'
    
    def __str__(self):
        return f"{self.position.title} - 分析数据"


class UserAnalytics(models.Model):
    """用户分析数据"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户', related_name='analytics')
    
    # 活跃度统计
    login_count = models.IntegerField('登录次数', default=0)
    last_login_date = models.DateField('最后登录日期', blank=True, null=True)
    active_days = models.IntegerField('活跃天数', default=0)
    
    # 浏览行为
    position_view_count = models.IntegerField('岗位浏览次数', default=0)
    exam_view_count = models.IntegerField('考试浏览次数', default=0)
    
    # 申请行为
    position_application_count = models.IntegerField('岗位申请次数', default=0)
    exam_registration_count = models.IntegerField('考试报名次数', default=0)
    
    # 收藏行为
    position_favorite_count = models.IntegerField('岗位收藏次数', default=0)
    
    # 推荐互动
    recommendation_received_count = models.IntegerField('收到推荐次数', default=0)
    recommendation_clicked_count = models.IntegerField('推荐点击次数', default=0)
    recommendation_applied_count = models.IntegerField('推荐申请次数', default=0)
    
    # 偏好分析
    preferred_locations = models.JSONField('偏好地区', blank=True, null=True)
    preferred_categories = models.JSONField('偏好分类', blank=True, null=True)
    preferred_salary_range = models.JSONField('偏好薪资范围', blank=True, null=True)
    
    # 成功率
    application_success_rate = models.DecimalField('申请成功率', max_digits=5, decimal_places=2, default=0)
    
    # 时间戳
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'analytics_user_analytics'
        verbose_name = '用户分析'
        verbose_name_plural = '用户分析'
    
    def __str__(self):
        return f"{self.user.display_name} - 分析数据"


class RecommendationAnalytics(models.Model):
    """推荐系统分析数据"""
    
    ALGORITHM_TYPE_CHOICES = [
        ('collaborative_filtering', '协同过滤'),
        ('content_based', '基于内容'),
        ('hybrid', '混合推荐'),
        ('popularity', '热门推荐'),
    ]
    
    # 算法信息
    algorithm_type = models.CharField('算法类型', max_length=50, choices=ALGORITHM_TYPE_CHOICES)
    date = models.DateField('统计日期')
    
    # 推荐统计
    total_recommendations = models.IntegerField('总推荐数', default=0)
    unique_users = models.IntegerField('推荐用户数', default=0)
    unique_positions = models.IntegerField('推荐岗位数', default=0)
    
    # 效果统计
    click_count = models.IntegerField('点击次数', default=0)
    application_count = models.IntegerField('申请次数', default=0)
    favorite_count = models.IntegerField('收藏次数', default=0)
    
    # 转化率
    click_through_rate = models.DecimalField('点击率', max_digits=5, decimal_places=2, default=0)
    application_rate = models.DecimalField('申请率', max_digits=5, decimal_places=2, default=0)
    conversion_rate = models.DecimalField('转化率', max_digits=5, decimal_places=2, default=0)
    
    # 用户反馈
    positive_feedback_count = models.IntegerField('正面反馈数', default=0)
    negative_feedback_count = models.IntegerField('负面反馈数', default=0)
    avg_rating = models.DecimalField('平均评分', max_digits=3, decimal_places=2, blank=True, null=True)
    
    # 覆盖率
    user_coverage = models.DecimalField('用户覆盖率', max_digits=5, decimal_places=2, default=0)
    position_coverage = models.DecimalField('岗位覆盖率', max_digits=5, decimal_places=2, default=0)
    
    # 时间戳
    calculated_at = models.DateTimeField('计算时间', default=timezone.now)
    
    class Meta:
        db_table = 'analytics_recommendation_analytics'
        verbose_name = '推荐分析'
        verbose_name_plural = '推荐分析'
        unique_together = ['algorithm_type', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.get_algorithm_type_display()} - {self.date}"
