# 数据库设计文档

## 概述

本文档描述了黔南州政策性考试成绩测试岗位推荐系统的数据库设计。

## 数据库表结构

### 1. 用户相关表

#### 用户表 (users_user)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| username | CharField | 150 | NO | - | 用户名 |
| email | EmailField | 254 | YES | - | 邮箱 |
| phone | CharField | 11 | YES | - | 手机号 |
| nickname | CharField | 50 | YES | - | 昵称 |
| avatar | ImageField | - | YES | - | 头像 |
| user_type | CharField | 20 | NO | candidate | 用户类型 |
| wechat_openid | CharField | 100 | YES | - | 微信OpenID |
| is_active | BooleanField | - | NO | True | 是否激活 |
| is_staff | BooleanField | - | NO | False | 是否管理员 |
| date_joined | DateTimeField | - | NO | now | 注册时间 |
| last_login | DateTimeField | - | YES | - | 最后登录时间 |

#### 考生信息表 (users_candidateprofile)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| user_id | ForeignKey | - | NO | - | 关联用户 |
| real_name | CharField | 50 | YES | - | 真实姓名 |
| id_card | CharField | 18 | YES | - | 身份证号 |
| gender | CharField | 10 | YES | - | 性别 |
| birth_date | DateField | - | YES | - | 出生日期 |
| education | CharField | 20 | YES | - | 学历 |
| major | CharField | 100 | YES | - | 专业 |
| graduation_year | IntegerField | - | YES | - | 毕业年份 |
| work_experience | TextField | - | YES | - | 工作经历 |
| address | CharField | 200 | YES | - | 地址 |
| created_at | DateTimeField | - | NO | now | 创建时间 |
| updated_at | DateTimeField | - | NO | now | 更新时间 |

### 2. 考试相关表

#### 考试信息表 (exams_exam)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| name | CharField | 200 | NO | - | 考试名称 |
| description | TextField | - | YES | - | 考试描述 |
| exam_type | CharField | 50 | NO | - | 考试类型 |
| start_date | DateTimeField | - | NO | - | 开始时间 |
| end_date | DateTimeField | - | NO | - | 结束时间 |
| registration_start | DateTimeField | - | YES | - | 报名开始时间 |
| registration_end | DateTimeField | - | YES | - | 报名结束时间 |
| status | CharField | 20 | NO | upcoming | 考试状态 |
| created_at | DateTimeField | - | NO | now | 创建时间 |
| updated_at | DateTimeField | - | NO | now | 更新时间 |

#### 考试成绩表 (exams_score)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| exam_id | ForeignKey | - | NO | - | 关联考试 |
| candidate_id | ForeignKey | - | NO | - | 关联考生 |
| initial_score | DecimalField | 5,2 | YES | - | 初评成绩 |
| assessment_score | DecimalField | 5,2 | YES | - | 考核测评成绩 |
| total_score | DecimalField | 5,2 | YES | - | 总成绩 |
| rank | IntegerField | - | YES | - | 排名 |
| is_qualified | BooleanField | - | NO | False | 是否合格 |
| created_at | DateTimeField | - | NO | now | 创建时间 |
| updated_at | DateTimeField | - | NO | now | 更新时间 |

#### 准考证表 (exams_admissionticket)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| exam_id | ForeignKey | - | NO | - | 关联考试 |
| candidate_id | ForeignKey | - | NO | - | 关联考生 |
| ticket_number | CharField | 50 | NO | - | 准考证号 |
| exam_room | CharField | 50 | YES | - | 考场 |
| seat_number | CharField | 10 | YES | - | 座位号 |
| exam_time | DateTimeField | - | YES | - | 考试时间 |
| created_at | DateTimeField | - | NO | now | 创建时间 |

### 3. 岗位相关表

#### 岗位表 (positions_position)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| title | CharField | 200 | NO | - | 岗位名称 |
| description | TextField | - | YES | - | 岗位描述 |
| department | CharField | 100 | NO | - | 部门 |
| location | CharField | 100 | NO | - | 工作地点 |
| salary_min | DecimalField | 10,2 | YES | - | 最低薪资 |
| salary_max | DecimalField | 10,2 | YES | - | 最高薪资 |
| education_required | CharField | 20 | YES | - | 学历要求 |
| major_required | CharField | 200 | YES | - | 专业要求 |
| experience_required | CharField | 100 | YES | - | 经验要求 |
| recruitment_count | IntegerField | - | NO | 1 | 招聘人数 |
| application_count | IntegerField | - | NO | 0 | 申请人数 |
| status | CharField | 20 | NO | active | 状态 |
| created_by_id | ForeignKey | - | YES | - | 创建者 |
| created_at | DateTimeField | - | NO | now | 创建时间 |
| updated_at | DateTimeField | - | NO | now | 更新时间 |

#### 岗位申请表 (positions_application)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| position_id | ForeignKey | - | NO | - | 关联岗位 |
| candidate_id | ForeignKey | - | NO | - | 关联考生 |
| status | CharField | 20 | NO | pending | 申请状态 |
| applied_at | DateTimeField | - | NO | now | 申请时间 |
| reviewed_at | DateTimeField | - | YES | - | 审核时间 |
| reviewed_by_id | ForeignKey | - | YES | - | 审核人 |
| notes | TextField | - | YES | - | 备注 |

#### 岗位收藏表 (positions_favorite)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| position_id | ForeignKey | - | NO | - | 关联岗位 |
| candidate_id | ForeignKey | - | NO | - | 关联考生 |
| created_at | DateTimeField | - | NO | now | 收藏时间 |

### 4. 推荐系统相关表

#### 用户行为表 (recommendations_userbehavior)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| user_id | ForeignKey | - | NO | - | 关联用户 |
| position_id | ForeignKey | - | NO | - | 关联岗位 |
| behavior_type | CharField | 20 | NO | - | 行为类型 |
| score | FloatField | - | NO | 1.0 | 行为分数 |
| created_at | DateTimeField | - | NO | now | 创建时间 |

#### 推荐记录表 (recommendations_recommendation)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| user_id | ForeignKey | - | NO | - | 关联用户 |
| position_id | ForeignKey | - | NO | - | 关联岗位 |
| algorithm_type | CharField | 50 | NO | - | 推荐算法类型 |
| score | FloatField | - | NO | - | 推荐分数 |
| reason | TextField | - | YES | - | 推荐理由 |
| created_at | DateTimeField | - | NO | now | 创建时间 |

### 5. 系统配置表

#### 系统配置表 (system_config)
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BigAutoField | - | NO | - | 主键 |
| key | CharField | 100 | NO | - | 配置键 |
| value | TextField | - | YES | - | 配置值 |
| description | CharField | 200 | YES | - | 描述 |
| created_at | DateTimeField | - | NO | now | 创建时间 |
| updated_at | DateTimeField | - | NO | now | 更新时间 |

## 索引设计

### 主要索引
1. users_user: username, email, wechat_openid
2. exams_score: exam_id, candidate_id, total_score
3. positions_position: location, department, status
4. positions_application: position_id, candidate_id, status
5. recommendations_userbehavior: user_id, position_id, behavior_type
6. recommendations_recommendation: user_id, score

### 复合索引
1. exams_score: (exam_id, total_score DESC)
2. positions_application: (candidate_id, status)
3. recommendations_userbehavior: (user_id, created_at DESC)

## 数据约束

### 外键约束
- 所有外键字段都设置了级联删除或保护约束
- 用户删除时，相关数据采用软删除方式

### 唯一约束
- users_user.username
- users_user.email
- users_user.wechat_openid
- exams_admissionticket.ticket_number
- positions_application: (position_id, candidate_id)
- positions_favorite: (position_id, candidate_id)

### 检查约束
- 成绩字段范围：0-100
- 薪资字段：大于0
- 招聘人数：大于0
